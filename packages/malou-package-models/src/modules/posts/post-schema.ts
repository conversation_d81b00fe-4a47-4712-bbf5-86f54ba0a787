import { v4 as uuidv4 } from 'uuid';

import {
    HashtagType,
    PlatformKey,
    postCallToActionTypeValues,
    PostPublicationStatus,
    PostSource,
    PostType,
    PublicationErrorCode,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
    TiktokPostPublishFailedReason,
    TiktokPrivacyStatus,
    urlRegex,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const postJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Post',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        platformId: {
            type: 'string',
            format: 'objectId',
        },
        socialId: {
            type: 'string',
        },
        socialCreatedAt: {
            type: 'string',
            format: 'date-time',
        },
        socialLink: {
            type: 'string',
            format: 'uri',
            match: urlRegex,
        },
        socialUpdatedAt: {
            type: 'string',
            format: 'date-time',
        },
        socialAttachments: {
            description: `Medias published on social platform, not owned by Malou. This field is set by SynchronizePostsUseCase.
            Use this only if 'attachments' are empty.

            For reels fetched from Facebook or Instagram, the thumbnail is socialAttachments[0].thumbnailUrl.`,
            type: 'array',
            items: {
                $ref: '#/definitions/SocialAttachment',
            },
        },
        attachments: {
            type: 'array',
            items: {
                anyOf: [
                    { type: 'string', format: 'objectId', ref: 'Media' },
                    {
                        type: 'null',
                    },
                ],
            },
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        hashtags: {
            $ref: '#/definitions/PostHashtags',
        },
        isReelDisplayedInFeed: {
            type: 'boolean',
            default: true,
        },
        // TODO change to platformKey
        key: {
            enum: Object.values(PlatformKey),
        },
        // platformsKeys used for drafts -> Change to platformKeys
        keys: {
            type: 'array',
            items: {
                enum: Object.values(PlatformKey),
            },
            default: [],
        },
        postTopic: {
            enum: Object.values(SeoPostTopic),
            default: SeoPostTopic.STANDARD,
        },
        postType: {
            enum: Object.values(PostType),
            default: PostType.IMAGE,
        },
        // TODO Change name -> publicationStatus ?
        published: {
            enum: Object.values(PostPublicationStatus),
            default: PostPublicationStatus.DRAFT,
        },
        isPublishing: {
            type: 'boolean',
            default: false,
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        shouldDuplicateInOtherPlatforms: {
            type: 'boolean',
            default: false,
        },
        source: {
            enum: Object.values(PostSource),
            default: PostSource.SOCIAL,
        },
        text: {
            type: 'string',
        },
        tries: {
            type: 'integer',
            default: 0,
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        userTags: {
            type: 'array',
            items: {
                $ref: '#/definitions/UserTagsList',
            },
        },
        userTagsList: {
            type: 'array',
            items: {
                type: 'array',
                nullable: true,
                items: {
                    $ref: '#/definitions/UserTagsList',
                },
            },
            description: 'List of user tags (Instagram) on each attachments',
        },
        language: {
            type: 'string',
        },
        plannedPublicationDate: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time',
                },
                {
                    type: 'null',
                },
            ],
        },
        author: {
            $ref: '#/definitions/Author',
        },
        authors: {
            type: 'array',
            items: {
                $ref: '#/definitions/Author',
            },
        },
        bindingId: {
            type: 'string',
            default: () => uuidv4(),
            description:
                'Used to do polling during the publication process. We delete the original post and create new ones (one by platform) with the same bindingId.',
        },
        duplicatedFromRestaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        callToAction: {
            $ref: '#/definitions/CallToAction',
            nullable: true,
        },
        event: {
            $ref: '#/definitions/Event',
        },
        location: {
            $ref: '#/definitions/PostSchemaLocation',
            nullable: true,
        },
        offer: {
            $ref: '#/definitions/Offer',
        },
        // the name we give to the attachments, used to improve local SEO
        attachmentsName: {
            type: 'string',
            nullable: true,
        },
        feedbackId: {
            anyOf: [
                {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Feedback',
                },
                {
                    type: 'null',
                },
            ],
        },
        isStory: {
            type: 'boolean',
            default: false,
        },
        title: {
            type: 'string',
        },
        errorData: {
            type: 'string',
            nullable: true,
        },
        errorStage: {
            type: 'string',
            nullable: true,
        },
        publicationErrors: {
            type: 'array',
            items: {
                $ref: '#/definitions/PublicationError',
            },
        },
        malouStoryId: {
            type: 'string',
        },
        keywordAnalysis: {
            $ref: '#/definitions/KeywordAnalysis',
        },
        thumbnail: {
            description: `This field is only used for reels.

            When a reel is edited on the Malou app, if the user selects a thumbnail for the reel (a frame
            or a custom one), this is the ID of the thumbnail (in the 'media' collection'). But once the
            reel is published this field will no longer be updated, so to reflect changes made directly
            on the external platform (without using Malou), you should always try to use
            'socialAttachments[0].thumbnailUrl' first, and then fallback on 'thumbnail' if nullish.
            In other words, the field 'thumbnail' is up-to-date until the reel is published, and after that
            we should use 'socialAttachments[0].thumbnailUrl' instead.

            If the reel has been created on the platform instead of the Malou app, this field will always be null.

            If the thumbnail is a frame extracted from the original video, the field thumbnailOffsetTimeInMs
            will be set in addition to this field. In this case, the 'thumbnail' field acts as a cache for
            the frame at thumbnailOffsetTimeInMs, since it’s created from the original video and thumbnailOffsetTimeInMs.

            If the thumbnail is a custom picture, the field thumbnailOffsetTimeInMs is nullish.`,
            anyOf: [
                {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Media',
                },
                {
                    type: 'null',
                },
            ],
        },
        thumbnailOffsetTimeInMs: {
            description: `only used for some reels, if the thumbnail is a frame extracted from the video on the Malou app.

            Just like the field 'thumbnail', if the reel has been created on the platform instead of the Malou app, this field will always be null! The thumbnail URL is socialAttachments[0].thumbnailUrl in this case.`,
            type: 'integer',
            nullable: true,
        },
        tiktokPublishId: {
            type: 'string',
            nullable: true,
        },
        tiktokOptions: {
            $ref: '#/definitions/TiktokOptions',
        },
        tiktokPublishFailureReason: {
            enum: Object.values(TiktokPostPublishFailedReason),
            nullable: true,
        },
        sortDate: {
            type: 'string',
            format: 'date-time',
            default: () => new Date(),
            description:
                'Computed date used for sorting and filtering. It is the socialCreatedAt value if it exists, else the plannedPublicationDate value if it exists, otherwise the updatedAt value. It is computed and set in the updateOne method of the post repository.',
        },
        instagramCollaboratorsUsernames: {
            type: 'array',
            items: { type: 'string' },
            description: 'A list a instagram usernames. Max 3.',
        },
    },
    required: [
        '_id',
        'createdAt',
        'postTopic',
        'postType',
        'published',
        'isStory',
        'source',
        'shouldDuplicateInOtherPlatforms',
        'updatedAt',
        'restaurantId',
        'keys',
    ],
    definitions: {
        Author: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                name: {
                    type: 'string',
                },
                lastname: {
                    type: 'string',
                },
                picture: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: ['_id'],
            title: 'Author',
        },
        CallToAction: {
            type: 'object',
            additionalProperties: false,
            properties: {
                actionType: {
                    enum: postCallToActionTypeValues,
                },
                url: {
                    type: 'string',
                },
            },
            required: ['actionType'],
            title: 'CallToAction',
        },
        Event: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                    nullable: true,
                },
                startDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
                endDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
            },
            required: [],
            title: 'Event',
        },
        KeywordAnalysis: {
            type: 'object',
            additionalProperties: false,
            properties: {
                keywords: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
                score: {
                    type: 'number',
                },
                count: {
                    type: 'integer',
                },
            },
            required: ['keywords'],
            title: 'KeywordAnalysis',
        },
        PostSchemaLocation: {
            type: 'object',
            additionalProperties: false,
            properties: {
                id: {
                    type: 'string',
                },
                name: {
                    type: 'string',
                },
                link: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
                location: {
                    $ref: '#/definitions/LocationLocation',
                },
            },
            required: ['id', 'link', 'name'],
            title: 'PostSchemaLocation',
        },
        LocationLocation: {
            type: 'object',
            additionalProperties: false,
            properties: {
                city: {
                    type: 'string',
                },
                country: {
                    type: 'string',
                },
                latitude: {
                    type: 'number',
                },
                longitude: {
                    type: 'number',
                },
                street: {
                    type: 'string',
                },
                zip: {
                    type: 'string',
                },
            },
            required: ['latitude', 'longitude'],
            title: 'LocationLocation',
        },
        Offer: {
            type: 'object',
            additionalProperties: false,
            properties: {
                couponCode: {
                    type: 'string',
                    nullable: true,
                },
                onlineUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                termsConditions: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
            title: 'Offer',
        },
        PostHashtags: {
            type: 'object',
            additionalProperties: false,
            properties: {
                selected: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/PostHashtag',
                    },
                },
                suggested: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/PostHashtag',
                    },
                },
            },
            title: 'PostHashtags',
        },
        SocialAttachment: {
            type: 'object',
            additionalProperties: false,
            properties: {
                urls: {
                    description: 'The domain name of this URL is typically a CDN of the platform (cdninstagram.com, fbcdn.net, ...)',
                    $ref: '#/definitions/Urls',
                },
                socialId: {
                    type: 'string',
                    nullable: true,
                },
                type: {
                    enum: Object.values(SocialAttachmentsMediaTypes),
                },
                thumbnailUrl: {
                    description: `
                    For reels, this URL is typically the picture of the custom thumbnail that should be displayed in the feed.
                    It’s not necessarily a frame extracted from the original video.

                    The domain name of this URL is typically a CDN of the platform (cdninstagram.com, fbcdn.net, ...)`,
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
            },
            required: ['type', 'urls'],
            title: 'SocialAttachment',
        },
        Urls: {
            type: 'object',
            additionalProperties: false,
            properties: {
                original: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                },
            },
            required: ['original'],
            title: 'Urls',
        },
        UserTagsList: {
            type: 'object',
            additionalProperties: false,
            properties: {
                username: {
                    type: 'string',
                },
                x: {
                    type: 'number',
                },
                y: {
                    type: 'number',
                },
            },
            required: ['username', 'x', 'y'],
            title: 'UserTagsList',
            description:
                '<x> and <y> are the coordinates of the tag on the image as a RATIO, not in pixels, their values are between 0 and 1',
        },
        PostHashtag: {
            type: 'object',
            additionalProperties: false,
            description: 'Represents a hashtag but for a post we removed restaurantId',
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                text: {
                    type: 'string',
                    minLength: 1,
                },
                isCustomerInput: {
                    type: 'boolean',
                    default: false,
                },
                isMain: {
                    type: 'boolean',
                },
                type: {
                    type: 'string',
                    enum: Object.values(HashtagType),
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['_id', 'createdAt', 'isCustomerInput', 'text', 'updatedAt', 'isMain', 'type'],
        },
        TiktokOptions: {
            type: 'object',
            additionalProperties: false,
            description: 'TikTok specific options',
            properties: {
                privacyStatus: {
                    enum: Object.values(TiktokPrivacyStatus),
                    default: TiktokPrivacyStatus.SELF_ONLY,
                },
                interactionAbility: {
                    type: 'object',
                    properties: {
                        duet: {
                            type: 'boolean',
                            default: false,
                        },
                        stitch: {
                            type: 'boolean',
                            default: false,
                        },
                        comment: {
                            type: 'boolean',
                            default: false,
                        },
                    },
                    required: ['duet', 'stitch', 'comment'],
                },
                contentDisclosureSettings: {
                    type: 'object',
                    properties: {
                        isActivated: {
                            type: 'boolean',
                        },
                        yourBrand: {
                            type: 'boolean',
                        },
                        brandedContent: {
                            type: 'boolean',
                        },
                    },
                    required: ['isActivated', 'yourBrand', 'brandedContent'],
                },
            },
            required: ['privacyStatus', 'interactionAbility', 'contentDisclosureSettings'],
            title: 'TiktokOptions',
        },
        PublicationError: {
            type: 'object',
            additionalProperties: false,
            properties: {
                data: {
                    description: 'A string that is likely to contains a stringified error',
                    type: 'string',
                },
                code: {
                    description: 'User facing error code. Take the most recent one in the array that is not empty and show it the to user',
                    enum: Object.values(PublicationErrorCode),
                },
                happenedAt: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['happenedAt'],
            title: 'PublicationError',
        },
    },
} as const satisfies JSONSchemaExtraProps;
