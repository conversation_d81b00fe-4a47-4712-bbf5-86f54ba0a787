import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import FormData from 'form-data';
import { DateTime } from 'luxon';
import qs from 'qs';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import {
    AppleBusinessConnectLocationState,
    AppleBusinessConnectMalouOperationType,
    AppleBusinessConnectResourceType,
    AppleBusinessConnectValidationReportPayload,
    AppleBusinessConnectValidationReportSeverity,
} from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.interfaces';
import {
    AppleBusinessApiCommonHeaders,
    AppleBusinessApiCommonResponseBody,
    AppleBusinessApiCreateLocationRequestBody,
    AppleBusinessApiCreateLocationResponseBody,
    AppleBusinessApiGetAccessTokenRequestBody,
    AppleBusinessApiGetAccessTokenResponseBody,
    AppleBusinessApiGetLocationByIdResponseBody,
    AppleBusinessApiGetLocationsResponseBody,
    AppleBusinessApiMediaUploadResponseBody,
    AppleBusinessApiUpdateLocationRequestBody,
    AppleBusinessApiUpdateLocationResponseBody,
} from ':providers/apple-business-connect/apple-business-connect.provider.interfaces';
import { ProviderMetricsService } from ':providers/provider.metrics.service';

@singleton()
export default class AppleBusinessConnectApiProvider {
    private axiosInstance: AxiosInstance;
    private accessToken: string | undefined;
    private accessTokenExpirationDate: Date | undefined;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        this.axiosInstance = axios.create({
            baseURL: `${Config.platforms.appleBusinessConnect.api.url}/api/${Config.platforms.appleBusinessConnect.api.version}`,
        });
    }

    // Create wrapper that calls again the function if the token is expired or invalid
    private async _callApi<T>(apiCall: () => Promise<AxiosResponse<T>>): Promise<T> {
        try {
            if (this._isTokenInvalid()) {
                logger.info('[AppleBusinessConnect] Access token is invalid, refreshing it');
                await this._refreshAccessToken();
            }

            const { data } = await apiCall();
            return data;
        } catch (error: any) {
            // Unauthorized probably because of expired token
            // Refresh it and try again
            if (error?.response?.status === 401) {
                logger.warn('[AppleBusinessConnect] API call was rejected with a 401, refreshing the token and retrying it');

                await this._refreshAccessToken();

                const { data } = await apiCall();
                return data;
            }

            throw error;
        }
    }

    private _isTokenInvalid(): boolean {
        if (!this.accessToken || !this.accessTokenExpirationDate) {
            return true;
        }

        if (this.accessTokenExpirationDate < new Date()) {
            return true;
        }

        return false;
    }

    private async _refreshAccessToken(): Promise<void> {
        const url = `${Config.platforms.appleBusinessConnect.api.url}/api/v1/oauth2/token`;

        const body: AppleBusinessApiGetAccessTokenRequestBody = {
            client_id: Config.platforms.appleBusinessConnect.api.clientId,
            client_secret: Config.platforms.appleBusinessConnect.api.clientSecret,
            grant_type: 'client_credentials',
            scope: 'business_connect',
        };

        const options: AxiosRequestConfig = {
            headers: {
                'content-type': 'application/json',
            },
        };

        const { access_token: accessToken, expires_in: expiresIn } = await this._providerMetricsService
            .callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'token.refresh',
                request: () => axios.post<AppleBusinessApiGetAccessTokenResponseBody>(url, body, options),
            })
            .then((res: AxiosResponse<AppleBusinessApiGetAccessTokenResponseBody>) => res.data);

        this.accessToken = accessToken;
        this.accessTokenExpirationDate = DateTime.now().plus({ seconds: expiresIn }).toJSDate();
    }

    private _getRequestHeaders(): AppleBusinessApiCommonHeaders {
        return {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.accessToken}`,
        };
    }

    private _logValidationReports<
        T extends AppleBusinessApiCommonResponseBody<Q> & {
            validationReports?: AppleBusinessConnectValidationReportPayload[];
        },
        Q,
    >(
        response: T,
        {
            resourceType,
            operationType,
        }: {
            resourceType: AppleBusinessConnectResourceType;
            operationType: AppleBusinessConnectMalouOperationType;
        }
    ): void {
        if (!response.validationReports) {
            return;
        }

        response.validationReports.forEach((report) => {
            const metadata = {
                ...report,
                operationType,
                resourceType,
                id: response.id,
                companyId: response.companyId,
                createdDate: response.createdDate,
                state: response.state,
            };

            if (report.severity === AppleBusinessConnectValidationReportSeverity.INFO) {
                logger.info('[AppleBusinessConnect] Sync validation report', metadata);
            } else if (report.severity === AppleBusinessConnectValidationReportSeverity.WARNING) {
                logger.warn('[AppleBusinessConnect] Sync validation report', metadata);
            } else if (report.severity === AppleBusinessConnectValidationReportSeverity.VIOLATION) {
                logger.error('[AppleBusinessConnect] Sync validation report', metadata);
            }
        });
    }

    // LOCATION
    // https://businessconnect.apple.com/docs/api/v3/location/create
    async createLocation({
        data,
    }: {
        data: AppleBusinessApiCreateLocationRequestBody;
    }): Promise<AppleBusinessApiCreateLocationResponseBody> {
        const url = `companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations`;

        const response = await this._callApi<AppleBusinessApiCreateLocationResponseBody>(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'location.create',
                request: () =>
                    this.axiosInstance.post(url, data, {
                        headers: this._getRequestHeaders(),
                    }),
            })
        );

        this._logValidationReports<AppleBusinessApiCreateLocationResponseBody, AppleBusinessConnectLocationState>(response, {
            resourceType: AppleBusinessConnectResourceType.LOCATION,
            operationType: AppleBusinessConnectMalouOperationType.CREATE,
        });

        return response;
    }

    // https://businessconnect.apple.com/docs/api/v3/location/update
    async updateLocation({
        locationId,
        etag,
        data,
    }: {
        locationId: string;
        etag: string;
        data: AppleBusinessApiUpdateLocationRequestBody;
    }): Promise<AppleBusinessApiUpdateLocationResponseBody> {
        const url = `companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations/${locationId}`;
        const response = await this._callApi<AppleBusinessApiUpdateLocationResponseBody>(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'location.update',
                request: () =>
                    this.axiosInstance.put(url, data, {
                        headers: {
                            ...this._getRequestHeaders(),
                            'if-match': etag,
                        },
                    }),
            })
        );

        this._logValidationReports<AppleBusinessApiUpdateLocationResponseBody, AppleBusinessConnectLocationState>(response, {
            resourceType: AppleBusinessConnectResourceType.LOCATION,
            operationType: AppleBusinessConnectMalouOperationType.UPDATE,
        });

        return response;
    }

    // MEDIA
    uploadMedia({ data }: { data: FormData }): Promise<AppleBusinessApiMediaUploadResponseBody> {
        const url = `companies/${Config.platforms.appleBusinessConnect.api.companyId}/images/upload`;

        return this._callApi<AppleBusinessApiMediaUploadResponseBody>(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'media.upload',
                request: () =>
                    this.axiosInstance.post(url, data, {
                        headers: {
                            ...this._getRequestHeaders(),
                            ...data.getHeaders(),
                        },
                    }),
            })
        );
    }

    // https://businessconnect.apple.com/docs/api/v3/location/get
    getLocations(params?: { query?: string; limit?: number; next?: string }): Promise<AppleBusinessApiGetLocationsResponseBody> {
        const query = qs.stringify({
            ...(params?.query && { ql: params?.query }),
            ...(params?.limit && { limit: params?.limit }),
        });
        const url = params?.next
            ? `${Config.platforms.appleBusinessConnect.api.url}/api${params?.next}`
            : `companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations${qs.stringify(query)}`;

        return this._callApi<AppleBusinessApiGetLocationsResponseBody>(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'location.getLocations',
                request: () => this.axiosInstance.get(url, { headers: this._getRequestHeaders() }),
            })
        );
    }

    // https://businessconnect.apple.com/docs/api/v3/location/get_by_id
    getLocationById({ locationId }: { locationId: string }): Promise<AppleBusinessApiGetLocationByIdResponseBody> {
        const url = `companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations/${locationId}`;

        return this._callApi<AppleBusinessApiGetLocationByIdResponseBody>(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'location.getById',
                request: () => this.axiosInstance.get(url, { headers: this._getRequestHeaders() }),
            })
        );
    }

    deleteLocation({ locationId, etag }: { locationId: string; etag: string }) {
        // eslint-disable-next-line max-len
        const url = `${Config.platforms.appleBusinessConnect.api.url}/api/${Config.platforms.appleBusinessConnect.api.version}/companies/${Config.platforms.appleBusinessConnect.api.companyId}/locations/${locationId}`;

        return this._callApi(() =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'apple-business-connect',
                requestId: 'location.delete',
                request: () =>
                    this.axiosInstance.delete(url, {
                        headers: {
                            ...this._getRequestHeaders(),
                            'if-match': etag,
                        },
                    }),
            })
        );
    }
}
