import { DateTime, Interval } from 'luxon';
import assert from 'node:assert';

import { isSameDay, MalouComparisonPeriod, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { BasicFilters } from ':helpers/filters/basic-filters';

/**
 * Class to format dates in the correct way depending on platform api used
 */
class PlatformInsightFiltersApi extends BasicFilters {
    static MAX_RANGE = 18;

    startDate: Date;
    endDate: Date;

    constructor(data: FiltersConstructorData) {
        super(data);
        this.startDate = new Date(data.startDate);
        this.endDate = new Date(data.endDate);
        if (this.constructor === PlatformInsightFiltersApi) {
            throw new MalouError(MalouErrorCode.FILTER_CANNOT_INITIATE_CLASS, {
                message: "can't instantiate PlatformInsightFilters class",
            });
        }

        this._checkTimeRange();
    }

    buildQuery(): any {
        throw new MalouError(MalouErrorCode.FILTER_METHOD_NOT_IMPLEMENTED, {
            message: 'Method buildQuery must be implemented in platform insight filters class.',
        });
    }

    _checkTimeRange() {
        if (!this.startDate || !this.endDate) {
            throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'start date & end date' }); // only when startDate & endDate = null + previousPeriod = true
        }

        const dtStart = DateTime.fromJSDate(this.startDate);
        const dtEnd = DateTime.fromJSDate(this.endDate);

        if (dtStart > dtEnd) {
            throw new MalouError(MalouErrorCode.FILTER_START_DATE_AFTER_END_DATE);
        }

        if (isSameDay(this.startDate, this.endDate)) {
            throw new MalouError(MalouErrorCode.FILTER_TIME_RANGE_TOO_SHORT, { message: 'Minimum time range is 1 day.' });
        }

        const { months } = DateTime.utc().diff(dtStart, 'months').toObject();
        assert(months, 'months should be defined');

        if (this instanceof PlatformInsightFiltersGmb && !this.previousPeriod && months >= PlatformInsightFiltersApi.MAX_RANGE) {
            throw new MalouError(MalouErrorCode.FILTER_TIME_RANGE_TOO_LONG, { message: 'Maximum start time is 18 months ago.' });
        }
    }
}

export class PlatformInsightFiltersGmb extends PlatformInsightFiltersApi {
    constructor(data: FiltersConstructorData) {
        super(data);
    }

    buildQuery() {
        return {
            startDate: this.startDate,
            endDate: this.endDate,
            previousPeriod: this.previousPeriod,
        };
    }
}

export class PlatformInsightFiltersFacebook extends PlatformInsightFiltersApi {
    constructor(data: FiltersConstructorData) {
        super(data);
    }

    buildQuery(duration = { days: 92 }) {
        // need to split dates because fb api doesnt allow a range over 90 days
        return this.startDate && this.endDate
            ? Interval.fromDateTimes(this.startDate, this.endDate)
                  .splitBy(duration)
                  .map((interval) => ({
                      since: interval.start.toJSDate().toISOString(),
                      until: interval.end.toJSDate().toISOString(),
                  }))
            : [];
    }
}

export class PlatformInsightFiltersInstagram extends PlatformInsightFiltersFacebook {
    buildQuery(duration = { days: 29 }) {
        return super.buildQuery(duration);
    }
}
interface FiltersConstructorData {
    startDate: string | Date;
    endDate: string | Date;
    previousPeriod?: boolean;
    comparisonPeriodData?: {
        comparisonPeriod: MalouComparisonPeriod;
        restaurantStartDate: Date;
    };
}

type PlatformInsightsFiltersResult<T> = T extends PlatformKey.GMB
    ? PlatformInsightFiltersGmb
    : T extends PlatformKey.FACEBOOK
      ? PlatformInsightFiltersFacebook
      : T extends PlatformKey.INSTAGRAM
        ? PlatformInsightFiltersInstagram
        : BasicFilters;

export class PlatformInsightFiltersApiFactory {
    static createPlatformInsightFiltersApi<T extends PlatformKey>(
        platformKey: T,
        data: FiltersConstructorData
    ): PlatformInsightsFiltersResult<T> {
        const authorizedPlatformsKeys = Object.values(PlatformKey);
        if (!authorizedPlatformsKeys.includes(platformKey)) {
            throw new MalouError(MalouErrorCode.FILTER_INVALID_INSIGHT_PLATFORM);
        }
        switch (platformKey) {
            case PlatformKey.GMB:
                return new PlatformInsightFiltersGmb(data) as PlatformInsightsFiltersResult<T>;
            case PlatformKey.FACEBOOK:
                return new PlatformInsightFiltersFacebook(data) as PlatformInsightsFiltersResult<T>;
            case PlatformKey.INSTAGRAM:
                return new PlatformInsightFiltersInstagram(data) as PlatformInsightsFiltersResult<T>;
            default:
                return new BasicFilters(data) as PlatformInsightsFiltersResult<T>;
        }
    }
}
