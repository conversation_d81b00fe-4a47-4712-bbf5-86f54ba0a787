import { singleton } from 'tsyringe';

import {
    DbId,
    EntityRepository,
    IMedia,
    IReport,
    IRestaurant,
    newDbId,
    PopulateBuilderHelper,
    ReportModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import { BusinessCategory, ReportType } from '@malou-io/package-utils';

import { Report as ReportEntity } from ':modules/reports/report.entity';
import { User, UserProps } from ':modules/users/entities/user.entity';
import { UsersRepository } from ':modules/users/users.repository';

type DeepPopulateReport = PopulateBuilderHelper<
    IReport,
    [
        {
            path: 'user';
        },
        {
            path: 'configurations';
            populate: [
                {
                    path: 'restaurants';
                    populate: [{ path: 'logo' }, { path: 'cover' }];
                },
            ];
        },
    ]
>;

@singleton()
export default class ReportsRepository extends EntityRepository<IReport> {
    constructor(private readonly _usersRepository: UsersRepository) {
        super(ReportModel);
    }

    private _createReportEntity(report: DeepPopulateReport | (IReport & { user?: UserProps & { _id: DbId } })): ReportEntity {
        return new ReportEntity({
            ...report,
            id: report._id.toString(),
            userId: report.userId.toString() ?? report.user?._id.toString(),
            user: report.user
                ? new User({
                      id: report.user._id.toString(),
                      email: report.user.email,
                      name: report.user.name,
                      lastname: report.user.lastname,
                      profilePicture: report.user.profilePicture?.toString(),
                      role: report.user.role,
                      verified: report.user.verified,
                      caslRole: report.user.caslRole,
                      createdAt: report.user.createdAt,
                      updatedAt: report.user.updatedAt,
                      organizationIds: report.user.organizationIds.map((o) => o?.toString()),
                      defaultLanguage: report.user.defaultLanguage,
                      expireSessionBefore: report.user.expireSessionBefore,
                      shouldExpireAbilitySession: report.user.shouldExpireAbilitySession,
                      hasV3Access: report.user.hasV3Access,
                      createdByUserId: report.user.createdByUserId?.toString(),
                      password: report.user.password,
                      hasBeenDeactivatedByAdmin: report.user.hasBeenDeactivatedByAdmin,
                      settings: {
                          ...report.user.settings,
                          notifications: {
                              ...report.user.settings.notifications,
                              web: {
                                  ...report.user.settings.notifications.web,
                                  filters: {
                                      ...report.user.settings.notifications.web?.filters,
                                      restaurantIds: report.user.settings.notifications.web?.filters?.restaurantIds.map((id) =>
                                          id.toString()
                                      ),
                                  },
                              },
                          },
                          receiveMessagesNotifications: {
                              ...report.user.settings.receiveMessagesNotifications,
                              restaurantsIds: report.user.settings.receiveMessagesNotifications?.restaurantsIds?.map((id) => id.toString()),
                          },
                      },
                  })
                : undefined,
            configurations:
                report.configurations?.map((config) => ({
                    id: config._id.toString(),
                    recipients: config.recipients.map((recipient) => recipient.toString()),
                    restaurantsIds: config?.restaurantsIds?.map((id) => id.toString()),
                    restaurants: config?.restaurants?.map((restaurant) => ({
                        ...restaurant,
                        logo: (restaurant.logo as IMedia)?.urls.small ?? (restaurant.logo as IMedia)?.urls.original,
                        id: restaurant._id.toString(),
                        _id: restaurant._id.toString(),
                        name: restaurant.internalName ?? restaurant.name,
                        type: restaurant.type,
                        address: restaurant.address?.formattedAddress,
                        // eslint-disable-next-line max-len
                        formattedAddress: `${restaurant.address?.formattedAddress}, ${restaurant.address?.postalCode} ${restaurant.address?.locality}`,
                    })),
                })) ?? [],
        });
    }

    async upsertReport(data: ReportEntity): Promise<ReportEntity> {
        const insertedReport = (await this.upsert({
            filter: {
                _id: data.id,
            },
            update: {
                type: data.type,
                active: data.active,
                _id: toDbId(data.id),
                configurations: data.configurations.map((config) => ({
                    ...config,
                    _id: toDbId(config.id),
                    restaurantsIds: config.restaurantsIds.map((id) => toDbId(id)),
                })),
                userId: toDbId(data.userId),
            },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'user',
                    },
                    {
                        path: 'configurations',
                        populate: [{ path: 'restaurants', populate: [{ path: 'logo' }, { path: 'cover' }] }],
                    },
                ],
            },
        })) as any as DeepPopulateReport;

        return this._createReportEntity(insertedReport);
    }

    async createReportsForUser(userId: DbId): Promise<void> {
        const reports = Object.values(ReportType).map((type) => ({
            type,
            userId,
            active: true,
            configurations: [],
        }));

        await this.createMany({ data: reports });
    }

    async getReportsByUserId({ userId }: { userId: string }): Promise<ReportEntity[]> {
        const reports = await this.find({
            filter: { userId },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'user',
                    },
                    {
                        path: 'configurations',
                        populate: [
                            {
                                path: 'restaurants',
                                populate: [{ path: 'logo' }, { path: 'cover' }],
                                select: { name: 1, logo: 1, cover: 1, type: 1, address: 1, internalName: 1 },
                                lean: true,
                            },
                        ],
                    },
                ],
            },
        });
        const leanReports = reports.map((report) => ({
            ...report,
            configurations: report.configurations.map((config) => ({
                ...config,
                restaurants: config.restaurants.map((restaurant) => ({
                    ...(restaurant as any).toJSON(),
                })),
            })),
        })) as any as DeepPopulateReport[];
        return leanReports.map((report) => this._createReportEntity(report));
    }

    async getReportsByReportType({ reportType }: { reportType: string }): Promise<Pick<IReport, '_id' | 'configurations'>[]> {
        return this.aggregate([
            {
                $match: { active: true, type: reportType },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'user',
                },
            },
            {
                $match: { user: { $ne: null } },
            },
            {
                $project: { _id: 1, configurations: 1 },
            },
        ]);
    }

    async getReportById({ reportId }: { reportId: string }): Promise<ReportEntity> {
        const report = (await this.findOne({
            filter: { _id: reportId, active: true },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'user',
                    },
                    {
                        path: 'configurations',
                        populate: [
                            {
                                path: 'restaurants',
                                populate: [{ path: 'logo' }, { path: 'cover' }],
                            },
                        ],
                    },
                ],
            },
        })) as any as DeepPopulateReport;

        return this._createReportEntity(report);
    }

    async getActiveReportsByRestaurantId({ restaurantId }: { restaurantId: string }): Promise<ReportEntity[]> {
        const reports = await this.find({
            filter: {
                'configurations.restaurantsIds': { $in: [toDbId(restaurantId)] },
                active: true,
            },
            options: { lean: true },
        });

        return reports.map((report) => this._createReportEntity(report));
    }

    async removeRestaurantIdFromAllConfigurations(restaurantId: DbId): Promise<void> {
        await this.updateMany({
            filter: { 'configurations.restaurantsIds': restaurantId },
            update: { $pull: { 'configurations.$[].restaurantsIds': restaurantId } },
        });

        await this.removeEmptyConfigurations();
    }

    async addRestaurantForUser(userId: DbId, restaurant: IRestaurant): Promise<void> {
        const [reports, user] = await Promise.all([
            this.find({
                filter: { userId },
            }),
            this._usersRepository.findOneOrFail({
                filter: { _id: userId },
            }),
        ]);

        const reportIds = reports
            .filter((report) => !report.type.includes('reviews') || restaurant.type === BusinessCategory.LOCAL_BUSINESS)
            .map((report) => report._id);

        await this.updateMany({
            filter: { _id: { $in: reportIds } },
            update: {
                $push: {
                    configurations: {
                        _id: newDbId(),
                        restaurantsIds: [restaurant._id],
                        recipients: [user.email],
                    },
                },
            },
        });
    }

    async removeUsersFromReports(userIds: string[]): Promise<void> {
        const users = await this._usersRepository.find({
            filter: { _id: { $in: toDbIds(userIds) } },
            projection: {
                email: 1,
            },
            options: {
                lean: true,
            },
        });
        const userEmails = users.map((user) => user.email);

        await Promise.all([
            this.deleteMany({ filter: { userId: { $in: userIds } } }),
            this.updateMany({
                filter: { 'configurations.recipients': { $in: userEmails } },
                update: {
                    $pull: {
                        'configurations.$[].recipients': {
                            $in: userEmails,
                        },
                    },
                },
            }),
        ]);

        await this.removeEmptyConfigurations();
    }

    async removeRestaurantForUser(userId: DbId, restaurantId: DbId): Promise<void> {
        const user = await this._usersRepository.findOneOrFail({ filter: { _id: userId }, options: { lean: true } });

        // Remove user's configurations with corresponding restaurantId
        await this.updateMany({
            filter: { userId: user._id, 'configurations.restaurantsIds': restaurantId },
            update: { $pull: { 'configurations.$[].restaurantsIds': restaurantId } },
        });

        // Remove restaurant's configurations with corresponding user email
        // These should be played in series to avoid removing user's email from its own remaining configurations
        await this.updateMany({
            filter: {
                'configurations.restaurantsIds': restaurantId,
                'configurations.recipients': user.email,
            },
            update: { $pull: { 'configurations.$[].recipients': user.email } },
        });

        await this.removeEmptyConfigurations();
    }

    async removeUserEmailFromConfiguration({ email, configurationId }: { email: string; configurationId: string }) {
        const report = await this.findOneAndUpdate({
            filter: {
                'configurations._id': toDbId(configurationId),
                'configurations.recipients': email,
            },
            update: {
                $pull: {
                    'configurations.$[elem].recipients': email,
                },
            },
            options: {
                arrayFilters: [{ 'elem._id': toDbId(configurationId) }],
            },
        });
        await this.removeEmptyConfigurations();

        return report;
    }

    async removeEmptyConfigurations(): Promise<void> {
        const reportsWithEmptyConfigurations = await this.find({
            filter: { $or: [{ 'configurations.recipients': { $size: 0 } }, { 'configurations.restaurantsIds': { $size: 0 } }] },
        });
        const configurationIdsToRemove = reportsWithEmptyConfigurations
            .map((report) =>
                report.configurations
                    .filter((config) => config.restaurantsIds.length === 0 || config.recipients.length === 0)
                    .map((config) => config._id)
            )
            .flat();

        await this._removeConfigurations(configurationIdsToRemove);
    }

    private async _removeConfigurations(configurationIds: DbId[]): Promise<void> {
        await this.updateMany({
            filter: { 'configurations._id': { $in: configurationIds } },
            update: { $pull: { configurations: { _id: configurationIds } } },
        });
    }
}
