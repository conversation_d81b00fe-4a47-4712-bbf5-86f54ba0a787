import { IReport, IReportConfiguration } from '@malou-io/package-dto';
import { Locale, mapApplicationLanguageToLocale, ReportType } from '@malou-io/package-utils';

import { Config } from ':config';
import { encodeString } from ':helpers/utils';
import { User } from ':modules/users/entities/user.entity';

export type ReportMetaData = {
    reportId: string;
    configurationId: string;
    recipients: string[];
    user?: {
        id: string;
        email: string;
        name: string;
        defaultLanguage: Locale;
    };
    restaurantIds: string[];
};

export class Report implements IReport {
    type: ReportType;
    configurations: IReportConfiguration[];
    userId: string;
    user?: User;
    active: boolean;
    id: string;
    createdAt: Date;
    updatedAt: Date;

    constructor(report: Omit<IReport, 'createdAt' | 'updatedAt'> & { createdAt?: Date; updatedAt?: Date } & { user?: User }) {
        this.id = report.id;
        this.type = report.type;
        this.configurations = report.configurations;
        this.userId = report.userId;
        this.active = report.active;
        this.createdAt = report.createdAt ? new Date(report.createdAt) : new Date();
        this.updatedAt = report.updatedAt ? new Date(report.updatedAt) : new Date();
        if (report.user) {
            this.user = new User(report.user);
        }
    }

    toDto() {
        return Object.freeze({
            type: this.type,
            configurations:
                this.configurations?.map((config) => ({
                    ...config,
                    restaurants: config.restaurants ?? [],
                })) ?? [],
            userId: this.userId,
            user: this.user,
            active: this.active,
            id: this.id,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        });
    }

    getRecipients(configurationIndex: number): string[] {
        return this.configurations[configurationIndex].recipients;
    }

    getReportId(): string {
        return this.id;
    }

    buildUnsubscribeLink({ email, config }: { email: string; config: Report['configurations'][0] }): string {
        const hash = encodeString(JSON.stringify({ reportType: this.type, configurationId: config.id, email }));

        return `${process.env.BASE_URL}/unsubscribe_report_v2?hash=${hash}&from_email=${this.type}&clicked_on=unsubscribe_link`;
    }

    buildTrackingUrl({ email, apiKey }: { email: string; apiKey: string }): string {
        return `${Config.baseApiUrl}/reports/emails/opened?reportId=${this.id}&receiverEmail=${email}&reportType=${
            this.type
        }&t=${new Date().getTime()}&api_key=${apiKey}`;
    }

    getMetaDataFromConfig(config: IReportConfiguration): ReportMetaData {
        return {
            reportId: this.id,
            configurationId: config.id,
            recipients: config.recipients,
            user: this.user
                ? {
                      id: this.user.id,
                      email: this.user.email,
                      name: this.user.name ?? '',
                      defaultLanguage: mapApplicationLanguageToLocale(this.user.defaultLanguage),
                  }
                : undefined,
            restaurantIds: config.restaurantsIds,
        };
    }

    getLogGroup(): string {
        return (
            {
                [ReportType.MONTHLY_PERFORMANCE]: '[Monthly performance report]',
                [ReportType.WEEKLY_PERFORMANCE]: '[Weekly performance report]',
                [ReportType.DAILY_REVIEWS]: '[Daily reviews report]',
                [ReportType.WEEKLY_REVIEWS]: '[Weekly reviews report]',
            }[this.type] ?? ''
        );
    }

    getBaseUrl(config: IReportConfiguration): string {
        return config.restaurants.length > 1
            ? `${process.env.BASE_URL}/groups`
            : `${process.env.BASE_URL}/restaurants/${config.restaurants[0]._id}`;
    }
}
