import { groupBy, isNil, uniqBy } from 'lodash';
import { singleton } from 'tsyringe';

import {
    SimpleRestaurant,
    SocialMediaPerformanceReportBestPostProps,
    SocialMediaPerformanceReportProps,
    socialMediaPerformanceReportValidator,
} from '@malou-io/package-dto';
import { ID } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    BusinessCategory,
    getGrowthVariation,
    isNotNil,
    isSameDay,
    MalouErrorCode,
    MalouMetric,
    numberToFixed,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { GetAggregatedInsightsForRestaurantAndPlatformUseCase } from ':modules/platform-insights/use-cases/get-aggregated-insights-for-restaurant-and-platform/get-aggregated-insights-for-restaurant-and-platform.use-case';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { InstagramPostMapper } from ':modules/posts/platforms/instagram/instagram-post-mapper';
import { PostInsightWithStats } from ':modules/posts/platforms/instagram/instagram-post.interface';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { Report } from ':modules/reports/report.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';

type InstagramPlatformsData = {
    _id: ID;
    socialId?: string;
    restaurantId: ID;
}[];

@singleton()
export class GetPerformanceSocialMediaSectionUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _getAggregatedInsightsForRestaurantAndPlatformUseCase: GetAggregatedInsightsForRestaurantAndPlatformUseCase
    ) {}

    async execute({
        report,
        config,
        periods,
    }: {
        report: Report;
        config: Report['configurations'][0];
        periods: IPerformancePeriods;
    }): Promise<SocialMediaPerformanceReportProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { startDate, endDate } = periods.current;
            const { uniqueInstagramAccounts, restaurantIdsGroupedBySocialId } = await this._getUniqueInstagramAccounts(config.restaurants);

            if (uniqueInstagramAccounts.length === 0) {
                return undefined;
            }

            const [currentStats, previousStats] = await Promise.all([
                this._computeStatsForAllInstagramPages({
                    instagramPlatforms: uniqueInstagramAccounts,
                    startDate,
                    endDate,
                    report,
                    config,
                }),
                this._computeStatsForAllInstagramPages({
                    instagramPlatforms: uniqueInstagramAccounts,
                    startDate: periods.previous.startDate,
                    endDate: periods.previous.endDate,
                    report,
                    config,
                }),
            ]);

            if (isNil(currentStats.followers) && isNil(currentStats.impressions) && isNil(currentStats.engagementRate)) {
                return undefined;
            }

            const bestPost = this._getBestPost({
                restaurantIdsGroupedBySocialId,
                post: currentStats?.posts?.[0],
                report,
                config,
            });

            const followersVariation = numberToFixed(
                currentStats.followers && previousStats.followers ? currentStats.followers - previousStats.followers : 0
            );
            const impressionsVariation = numberToFixed(
                currentStats.impressions && previousStats.impressions ? currentStats.impressions - previousStats.impressions : 0
            );
            const engagementRateVariation = numberToFixed(
                currentStats.engagementRate && previousStats.engagementRate ? currentStats.engagementRate - previousStats.engagementRate : 0
            );

            const returnData: SocialMediaPerformanceReportProps = {
                instagram: {
                    variation: getGrowthVariation(engagementRateVariation),
                    ...(currentStats.followers !== undefined && {
                        subscriptions: {
                            totalRated: currentStats.followers,
                            growth: {
                                rate: followersVariation,
                                variation: getGrowthVariation(followersVariation),
                                flipped: false,
                            },
                        },
                    }),
                    ...(currentStats.impressions !== undefined && {
                        impressions: {
                            totalRated: currentStats.impressions,
                            growth: {
                                rate: impressionsVariation,
                                variation: getGrowthVariation(impressionsVariation),
                                flipped: false,
                            },
                        },
                    }),
                    ...(currentStats.engagementRate !== undefined && {
                        engagement: {
                            totalRated: numberToFixed(currentStats.engagementRate, 2),
                            growth: {
                                rate: engagementRateVariation,
                                variation: getGrowthVariation(engagementRateVariation),
                                flipped: false,
                                isPercentage: true,
                            },
                        },
                    }),
                    nbOfPosts: currentStats.nbOfPosts,
                    ...(bestPost && { bestPost }),
                },
            };

            socialMediaPerformanceReportValidator.parse(returnData);

            return returnData;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Instagram stats section`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private _getBestPost({
        restaurantIdsGroupedBySocialId,
        post,
        report,
        config,
    }: {
        restaurantIdsGroupedBySocialId: Record<string, string[]>;
        post: PostInsightWithStats & { socialPlatformId: string };
        report: Report;
        config: Report['configurations'][0];
    }): SocialMediaPerformanceReportBestPostProps | undefined {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            // Undefined or engagement rate at 0%
            if (!post || !post?.stats?.engagementRate) {
                return undefined;
            }

            // Get original posting restaurant
            const restaurantIds = restaurantIdsGroupedBySocialId[post.socialPlatformId];
            const restaurant = config.restaurants.find(
                (r) => restaurantIds.includes(r._id.toString()) && r.type === BusinessCategory.LOCAL_BUSINESS
            );

            return {
                metadata: {
                    type: post.postType,
                    image: post.thumbnailUrl,
                    ...(restaurant && {
                        restaurant: {
                            address: restaurant.formattedAddress,
                            name: restaurant.name,
                        },
                    }),
                },
                engagement: post.stats.engagementRate,
                impressions: post.stats.impressions,
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} Instagram stats section, get best post`, {
                err,
                postId: post?.socialId,
                platformId: post?.socialPlatformId,
                ...metaData,
            });

            return undefined;
        }
    }

    // Get unique instagram accounts corresponding to restaurants
    // And mapping between socialId and restaurantIds for best post's restaurant
    private async _getUniqueInstagramAccounts(restaurants: SimpleRestaurant[]): Promise<{
        uniqueInstagramAccounts: InstagramPlatformsData;
        restaurantIdsGroupedBySocialId: Record<string, string[]>;
    }> {
        const instagramAccounts = await this._platformsRepository.find({
            filter: {
                restaurantId: { $in: restaurants.map((restaurant) => restaurant._id) },
                key: PlatformKey.INSTAGRAM,
            },
            projection: { socialId: 1, restaurantId: 1 },
            options: { lean: true },
        });
        const platformGroupedBySocialId = groupBy(
            instagramAccounts.filter(({ socialId }) => isNotNil(socialId)),
            (platform) => platform.socialId
        );

        const restaurantIdsGroupedBySocialId: Record<string, string[]> = {};
        Object.keys(platformGroupedBySocialId).forEach(
            (socialId) =>
                (restaurantIdsGroupedBySocialId[socialId] = platformGroupedBySocialId[socialId].map(({ restaurantId }) =>
                    restaurantId.toString()
                ))
        );

        const uniqueInstagramAccounts = instagramAccounts.map((platform) => ({
            ...platform,
            socialId: platform.socialId,
        }));
        return {
            uniqueInstagramAccounts: uniqBy(uniqueInstagramAccounts, (platform) => platform.socialId).map((uniqueInstagramAccount) => ({
                ...uniqueInstagramAccount,
                socialId: uniqueInstagramAccount.socialId ?? undefined,
            })),
            restaurantIdsGroupedBySocialId,
        };
    }

    private async _computeStatsForAllInstagramPages({
        instagramPlatforms,
        report,
        config,
        startDate,
        endDate,
    }: {
        instagramPlatforms: InstagramPlatformsData;
        report: Report;
        config: Report['configurations'][0];
        startDate: Date;
        endDate: Date;
    }): Promise<{
        followers: number | undefined;
        impressions: number | undefined;
        engagementRate: number | undefined;
        posts: (PostInsightWithStats & { socialPlatformId: string })[];
        nbOfPosts: number;
    }> {
        const instagramStats = (
            await Promise.all(
                instagramPlatforms.map(async (instagramPlatform) =>
                    this._computeStatsPerInstagramPage({
                        instagramPlatform,
                        report,
                        config,
                        startDate,
                        endDate,
                    })
                )
            )
        ).filter((stats) => stats !== undefined && stats !== null);

        const filteredFollowersStats = filterByRequiredKeys(instagramStats, ['followers']);
        const followers =
            filteredFollowersStats.length === 0 ? undefined : filteredFollowersStats.reduce((acc, curr) => acc + curr.followers, 0);

        const impressionStats = filterByRequiredKeys(instagramStats, ['impressions']);
        const impressions = impressionStats.length === 0 ? undefined : impressionStats.reduce((acc, curr) => acc + curr.impressions, 0);

        const engagementRates = filterByRequiredKeys(instagramStats, ['engagementRate']);
        const engagementRate =
            engagementRates.length === 0
                ? undefined
                : engagementRates.reduce((acc, curr) => acc + curr.engagementRate, 0) / engagementRates.length;

        // Sort posts by engagementRate
        const instagramPosts: (PostInsightWithStats & { socialPlatformId: string })[] = instagramStats.reduce(
            (acc: (PostInsightWithStats & { socialPlatformId: string })[], curr) => [...acc, ...curr.postsData],
            []
        );
        const posts = instagramPosts
            .filter((post) => isNotNil(post.stats.engagementRate))
            .sort((a, b) => b.stats.engagementRate! - a.stats.engagementRate!);

        return {
            followers,
            impressions,
            engagementRate,
            posts,
            nbOfPosts: instagramPosts.length,
        };
    }

    private async _computeStatsPerInstagramPage({
        instagramPlatform,
        report,
        config,
        startDate,
        endDate,
    }: {
        instagramPlatform: InstagramPlatformsData[0];
        report: Report;
        config: Report['configurations'][0];
        startDate: Date;
        endDate: Date;
    }): Promise<
        | {
              followers: number | undefined;
              impressions: number | undefined;
              engagementRate: number | undefined;
              postsData: (PostInsightWithStats & { socialPlatformId: string })[];
          }
        | undefined
    > {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { restaurantId } = instagramPlatform;

            const [insights, posts] = await Promise.all([
                this._getAggregatedInsightsForRestaurantAndPlatformUseCase.execute({
                    restaurantId: restaurantId.toString(),
                    platformKey: PlatformKey.INSTAGRAM,
                    insightsConditions: {
                        metrics: [MalouMetric.FOLLOWERS],
                        aggregators: [AggregationTimeScale.TOTAL, AggregationTimeScale.BY_DAY],
                    },
                    periodConditions: {
                        startDate,
                        endDate,
                        previousPeriod: false,
                    },
                }),
                this._postsUseCases.fetchPostsWithInsights({
                    restaurantId: restaurantId.toString(),
                    startDate: startDate.toString(),
                    endDate: endDate.toString(),
                    platformKeys: [PlatformKey.INSTAGRAM],
                }),
            ]);

            if (!insights) {
                throw new MalouError(MalouErrorCode.PLATFORM_INSIGHTS_ERROR, {
                    message: '[_computeStatsPerInstagramPage] Platform insights not found',
                    metadata: { platformId: instagramPlatform._id, restaurantId: instagramPlatform.restaurantId },
                });
            }
            const { total, by_day, error: errorFromPlatformInsights, message: errorMessageFromPlatformInsights } = insights;

            if (errorFromPlatformInsights || posts[0].instagram.error) {
                throw new Error(errorMessageFromPlatformInsights ?? posts[0].instagram.message);
            }

            const instagramSocialId = instagramPlatform.socialId;
            if (!instagramSocialId) {
                throw new MalouError(MalouErrorCode.PLATFORM_MISSING_SOCIAL_ID, {
                    message: '[_computeStatsPerInstagramPage] Instagram platform missing socialId',
                    metadata: { platformId: instagramPlatform._id, restaurantId: instagramPlatform.restaurantId },
                });
            }

            // Enhance posts for stats
            const postsData: (PostInsightWithStats & { socialPlatformId: string })[] =
                posts[0].instagram.data?.map((postInsight: any) => {
                    // Find nb of followers at post creation date
                    const nbOfFollowersAtCreationDate = by_day?.followers?.find((date) =>
                        isSameDay(date.date, postInsight.createdAt)
                    )?.value;

                    const mappedPost = new InstagramPostMapper().mapToPostInsightsStatistics(postInsight, nbOfFollowersAtCreationDate);

                    return {
                        ...mappedPost,
                        // Used to fetch corresponding restaurant afterwards
                        socialPlatformId: instagramSocialId,
                    };
                }) ?? [];

            const followers = total?.followers?.value;
            const filteredImpressionsData = postsData.filter((post) => post.stats?.impressions !== undefined);
            const impressions =
                filteredImpressionsData.length === 0
                    ? undefined
                    : filteredImpressionsData.reduce((acc, curr) => acc + curr.stats.impressions, 0);
            const engagementRate =
                followers === undefined || followers === 0 || postsData.length === 0
                    ? undefined
                    : (postsData.reduce((acc, curr) => acc + curr.stats.interactions, 0) / followers / postsData.length) * 100;

            return {
                followers,
                impressions,
                engagementRate,
                postsData,
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} Instagram stats section, get single insta data`, {
                err,
                platformId: instagramPlatform._id,
                socialId: instagramPlatform.socialId,
                ...metaData,
            });

            return undefined;
        }
    }
}
