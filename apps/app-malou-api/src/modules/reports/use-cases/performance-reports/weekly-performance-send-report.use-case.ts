import { render } from '@react-email/render';
import assert from 'assert';
import { isNil } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    GmbPerformanceReportValidatorProps,
    IncomingEventsPerformanceReportProps,
    NotAnsweredMessagesPerformanceReportProps,
    ReviewsStatsPerformanceReportProps,
    SocialMediaPerformanceReportProps,
    WeeklyPerformanceReportProps,
} from '@malou-io/package-dto';
import { WeeklyPerformanceReport } from '@malou-io/package-emails';
import {
    MalouErrorCode,
    mapApplicationLanguageToLocale,
    processPromisesByChunks,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { Report } from ':modules/reports/report.entity';
import { ReportSendParamsValidatorDto } from ':modules/reports/reports.dto';
import ReportsRepository from ':modules/reports/reports.repository';
import { DeleteReportPDFService } from ':modules/reports/services/delete-report-pdf.service';
import { GenerateReportPDFService } from ':modules/reports/services/generate-report-pdf.service';
import { SendReportEmailService } from ':modules/reports/services/send-report-email.service';
import { PerformanceWeeklyReport } from ':modules/reports/use-cases/performance-reports/performance-report-weekly.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';
import { GetPerformanceCalendarEventsSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-calendar-events-section.use-case';
import { GetGmbPostsWeeklyPerformanceRecommendationsUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-gmb-posts-recommendation-weekly.use-case';
import { GetPerformanceGmbSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-gmb-section.use-case';
import { GetPerformanceMessagesSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-messages-section.use-case';
import { GetPerformanceReviewsSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-reviews-section.use-case';
import { GetSocialMediaPostsWeeklyPerformanceRecommendationsUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-social-media-posts-recommendation-weekly.use-case';
import { GetPerformanceSocialMediaSectionUseCase } from ':modules/reports/use-cases/performance-reports/sections/performance-social-media-section.use-case';

@singleton()
export class WeeklyPerformanceSendReportUseCase {
    private _apiKey!: string;
    private readonly CALENDAR_EVENTS_UPCOMING_WEEKS = 2;
    private readonly PLANNED_GMB_POSTS_UPCOMING_WEEKS = 2;
    private readonly PLANNED_SOCIAL_POSTS_UPCOMING_WEEKS = 2;
    private readonly SEND_REPORT_EMAILS_CHUNK_SIZE = 5;
    private readonly INTERVAL_BETWEEN_EMAILS = 5;

    constructor(
        private readonly _reportsRepository: ReportsRepository,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _getPerformanceCalendarEventsSectionUseCase: GetPerformanceCalendarEventsSectionUseCase,
        private readonly _getGmbPostsWeeklyPerformanceRecommendationsUseCase: GetGmbPostsWeeklyPerformanceRecommendationsUseCase,
        // eslint-disable-next-line max-len
        private readonly _getSocialMediaPostsWeeklyPerformanceRecommendationsUseCase: GetSocialMediaPostsWeeklyPerformanceRecommendationsUseCase,
        private readonly _getPerformanceGmbSectionUseCase: GetPerformanceGmbSectionUseCase,
        private readonly _getPerformanceReviewsSectionUseCase: GetPerformanceReviewsSectionUseCase,
        private readonly _getPerformanceMessagesSectionUseCase: GetPerformanceMessagesSectionUseCase,
        private readonly _getPerformanceSocialNetworksSectionUseCase: GetPerformanceSocialMediaSectionUseCase,
        private readonly _sendReportEmailService: SendReportEmailService,
        private readonly _generateReportPDFService: GenerateReportPDFService,
        private readonly _deleteReportPDFService: DeleteReportPDFService
    ) {}

    async execute({ reportId, configurationId, options }: ReportSendParamsValidatorDto): Promise<void> {
        const [key, report] = await Promise.all([
            this._apiKeysRepository.findOne({
                filter: { name: 'email' },
                options: { lean: true },
            }),
            this._reportsRepository.getReportById({ reportId }),
        ]);

        assert(
            report,
            new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: { reportId },
            })
        );
        assert(key, '[WeeklyPerformanceSendReportUseCase] API key not found');
        this._apiKey = key.apiKey;

        const configuration = report?.configurations?.find((config) => config.id === configurationId);

        if (isNil(report) || isNil(configuration)) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Report not found',
                metadata: {
                    reportId,
                    configurationId,
                },
            });
        }

        // For debug, send to custom emails
        if (options?.sendTo) {
            configuration.recipients = options.sendTo;
        }

        const reportData = await this._getPerformanceWeeklyReport(report, configuration);
        assert(reportData, '[WeeklyPerformanceSendReportUseCase] Report data not found');

        const sendReports = await this._sendReports(reportData, options?.shouldPreventEmailSending);

        await processPromisesByChunks(sendReports, this.SEND_REPORT_EMAILS_CHUNK_SIZE, async (_) => {
            await waitFor(this.INTERVAL_BETWEEN_EMAILS * TimeInMilliseconds.SECOND);
        });

        await this._deleteReportPdf(reportData);
    }

    private async _getPerformanceWeeklyReport(
        report: Report,
        config: Report['configurations'][0]
    ): Promise<PerformanceWeeklyReport | null> {
        const metaData = report.getMetaDataFromConfig(config);
        const reportLogGroup = report.getLogGroup();

        try {
            logger.info(`${reportLogGroup} Get report data`, metaData);

            const data = await this._getReportData({
                report,
                config,
            });

            const emailProps: WeeklyPerformanceReportProps = {
                concernedRestaurants: config.restaurants,
                incomingEvents: data.incomingEvents,
                gmb: data.gmbInsights,
                reviews: data.reviews,
                socialMedia: data.socialMedia,
                notAnsweredMessages: data.notAnsweredMessages,
                locale: mapApplicationLanguageToLocale(report.user?.defaultLanguage),
                period: data.period,
            };

            const performanceWeeklyReport = new PerformanceWeeklyReport(emailProps, report, config);

            const pdfFileUrl = await this._generateReportPDFService.execute({
                report,
                configuration: config,
                pdfFolderName: performanceWeeklyReport.getPdfFolderName(),
                pdfFileName: performanceWeeklyReport.getPdfFileName(),
                html: render(WeeklyPerformanceReport(emailProps)),
            });
            if (pdfFileUrl) {
                performanceWeeklyReport.setPdfFileUrl(pdfFileUrl);
            }

            return performanceWeeklyReport;
        } catch (err) {
            logger.error(`${reportLogGroup} Get report data`, {
                err,
                ...metaData,
            });
            return null;
        }
    }

    private _sendReports(performanceWeeklyReport: PerformanceWeeklyReport, shouldPreventEmailSending?: boolean): (() => Promise<void>)[] {
        const configurationMetaData = performanceWeeklyReport.getMetaData();
        const reportLogGroup = performanceWeeklyReport.getLogGroup();

        logger.info(`${reportLogGroup} Preparing to send reports`, {
            ...configurationMetaData,
            recipientsCount: performanceWeeklyReport.getRecipients().length,
        });

        const emailsPromises: (() => Promise<void>)[] = [];

        for (const recipient of performanceWeeklyReport.getRecipients()) {
            emailsPromises.push(() => this._sendReport(performanceWeeklyReport, recipient, shouldPreventEmailSending));
        }

        return emailsPromises;
    }

    private async _sendReport(
        performanceWeeklyReport: PerformanceWeeklyReport,
        recipient: string,
        shouldPreventEmailSending?: boolean
    ): Promise<void> {
        const metaData = {
            recipient,
            ...performanceWeeklyReport.getMetaData(),
        };
        const reportLogGroup = performanceWeeklyReport.getLogGroup();

        try {
            logger.info(`${reportLogGroup} Preparing to send report`, metaData);

            const unsubscribeLink = await performanceWeeklyReport.buildUnsubscribeLink(recipient);
            const trackingUrl = performanceWeeklyReport.buildTrackingUrl({ email: recipient, apiKey: this._apiKey });

            const html = render(
                WeeklyPerformanceReport({
                    ...performanceWeeklyReport.getEmailProps(),
                    unsubscribeLink,
                    trackingUrl,
                })
            );

            await this._sendReportEmailService.execute({
                report: performanceWeeklyReport.getReport(),
                recipient,
                configuration: performanceWeeklyReport.getConfiguration(),
                reportEmailSubject: performanceWeeklyReport.getReportEmailSubject(),
                language: performanceWeeklyReport.getEmailProps().locale,
                pdfFileUrl: performanceWeeklyReport.getPdfFileUrl(),
                html,
                shouldPreventEmailSending,
            });
        } catch (err) {
            logger.error(`${reportLogGroup} Failed to send report`, {
                err,
                ...metaData,
            });
        }
    }

    async _deleteReportPdf(reportData: PerformanceWeeklyReport): Promise<void> {
        const pdfFileUrl = reportData.getPdfFileUrl();
        if (isNil(pdfFileUrl)) {
            logger.warn(`${reportData.getLogGroup()} PDF file not found`, reportData.getMetaData());
            return;
        }

        await this._deleteReportPDFService.execute({
            report: reportData.getReport(),
            configuration: reportData.getConfiguration(),
            pdfFolderName: reportData.getPdfFolderName(),
            pdfFileName: reportData.getPdfFileName(),
            pdfFileUrl,
        });
    }

    private async _getReportData({ report, config }: { report: Report; config: Report['configurations'][0] }): Promise<{
        incomingEvents: IncomingEventsPerformanceReportProps | undefined;
        gmbInsights: GmbPerformanceReportValidatorProps | undefined;
        reviews: ReviewsStatsPerformanceReportProps | undefined;
        socialMedia?: SocialMediaPerformanceReportProps;
        notAnsweredMessages: NotAnsweredMessagesPerformanceReportProps | undefined;
        period: IPerformancePeriods['current'];
    }> {
        const periods: IPerformancePeriods = {
            current: {
                startDate: DateTime.now().minus({ weeks: 1 }).startOf('week').toJSDate(),
                endDate: DateTime.now().minus({ weeks: 1 }).endOf('week').toJSDate(),
            },
            previous: {
                startDate: DateTime.now().minus({ weeks: 2 }).startOf('week').toJSDate(),
                endDate: DateTime.now().minus({ weeks: 2 }).endOf('week').toJSDate(),
            },
        };

        const [
            incomingEvents,
            gmbInsights,
            gmbPostsRecommendation,
            reviews,
            socialMedia,
            socialMediaPostsRecommendation,
            notAnsweredMessages,
        ] = await Promise.all([
            this._getPerformanceCalendarEventsSectionUseCase.execute({
                report,
                config,
                periods: {
                    startDate: DateTime.now().toJSDate(),
                    endDate: DateTime.now().plus({ weeks: this.CALENDAR_EVENTS_UPCOMING_WEEKS }).toJSDate(),
                },
            }),
            this._getPerformanceGmbSectionUseCase.execute({ report, config, periods }),
            this._getGmbPostsWeeklyPerformanceRecommendationsUseCase.execute({
                report,
                config,
                startDate: DateTime.now().toJSDate(),
                endDate: DateTime.now().plus({ weeks: this.PLANNED_GMB_POSTS_UPCOMING_WEEKS }).toJSDate(),
            }),
            this._getPerformanceReviewsSectionUseCase.execute({ report, config, periods }),
            this._getPerformanceSocialNetworksSectionUseCase.execute({ report, config, periods }),
            this._getSocialMediaPostsWeeklyPerformanceRecommendationsUseCase.execute({
                report,
                config,
                startDate: DateTime.now().toJSDate(),
                endDate: DateTime.now().plus({ weeks: this.PLANNED_SOCIAL_POSTS_UPCOMING_WEEKS }).toJSDate(),
            }),
            this._getPerformanceMessagesSectionUseCase.execute({ report, config, periods }),
        ]);

        return {
            incomingEvents,
            gmbInsights: gmbInsights
                ? {
                      ...gmbInsights,
                      postsWeeklyRecommendation: gmbPostsRecommendation,
                  }
                : undefined,
            reviews,
            ...(socialMedia && {
                socialMedia: {
                    instagram: {
                        ...socialMedia?.instagram,
                        postsWeeklyRecommendation: socialMediaPostsRecommendation,
                    },
                },
            }),
            notAnsweredMessages,
            period: periods.current,
        };
    }
}
