import { uniqBy } from 'lodash';
import { autoInjectable } from 'tsyringe';

import { ReportType } from '@malou-io/package-utils';

import { ReportSendOptionsValidatorDto, ReportSendParamsValidatorDto } from ':modules/reports/reports.dto';
import ReportsRepository from ':modules/reports/reports.repository';

@autoInjectable()
export class GetReportsToSendUseCase {
    constructor(private readonly _reportsRepository: ReportsRepository) {}

    async execute(
        reportType: ReportType,
        options: ReportSendOptionsValidatorDto
    ): Promise<{
        sendingStats: {
            reportsCount: number;
            recipientsCount: number;
        };
        producerBodyMessages: ReportSendParamsValidatorDto[];
    }> {
        let reports = await this._reportsRepository.getReportsByReportType({ reportType });
        if (options.limit) {
            reports = reports.slice(0, options.limit);
        }

        const sendingStats = {
            reportsCount: 0,
            recipientsCount: 0,
        };

        const producerBodyMessages: ReportSendParamsValidatorDto[] = [];

        const validConfigurations = reports.flatMap((report) =>
            report.configurations
                .filter((config) => config.recipients.length > 0 && config.restaurantsIds.length > 0)
                .map((e) => ({ ...e, reportId: report._id }))
        );
        const uniqConfigurations = uniqBy(validConfigurations, (configuration) => {
            const sortedRecipients = [...configuration.recipients].sort();
            const sortedRestaurantIds = configuration.restaurantsIds.map((id) => id.toString()).sort();
            return JSON.stringify({ sortedRecipients, sortedRestaurantIds });
        });

        uniqConfigurations.forEach((configuration) => {
            sendingStats.reportsCount++;
            sendingStats.recipientsCount += configuration.recipients.length;

            producerBodyMessages.push({
                reportId: configuration.reportId.toString(),
                configurationId: configuration._id.toString(),
                options,
            });
        });

        return {
            sendingStats,
            producerBodyMessages,
        };
    }
}
