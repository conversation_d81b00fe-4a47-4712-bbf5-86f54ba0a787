import assert from 'assert';
import { singleton } from 'tsyringe';

import { AggregationTimeScale, MalouComparisonPeriod, MalouMetric, PlatformKey } from '@malou-io/package-utils';

import { PlatformInsightFiltersApiFactory } from ':helpers/filters/platform-insight-filters-api-factory';
import { getPlatformInsights } from ':modules/platform-insights/platform-insights.getter';
import { TimeScaleToMetricToDataValues } from ':modules/platform-insights/platform-insights.types';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetAggregatedInsightsForRestaurantAndPlatformUseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(
        {
            restaurantId,
            platformKey,
            insightsConditions,
            periodConditions,
        }: {
            restaurantId: string;
            platformKey: PlatformKey;
            insightsConditions: { metrics: MalouMetric[]; aggregators: AggregationTimeScale[] };
            periodConditions: {
                startDate: Date;
                endDate: Date;
                previousPeriod: boolean;
                comparisonPeriod?: MalouComparisonPeriod;
            };
        },
        // TODO: removed this parameter later [@hamza]
        isAggregatedSocialMediaPerformanceReleaseEnabled?: boolean
    ): Promise<TimeScaleToMetricToDataValues | undefined> {
        const { metrics, aggregators } = insightsConditions;
        const { startDate, endDate, comparisonPeriod, previousPeriod } = periodConditions;

        const filtersInput: {
            startDate: Date;
            endDate: Date;
            previousPeriod: boolean;
            comparisonPeriodData?: { comparisonPeriod: MalouComparisonPeriod; restaurantStartDate: Date };
        } = {
            startDate,
            endDate,
            previousPeriod,
        };
        if (comparisonPeriod) {
            const restaurantWithCreatedAt = await this._restaurantsRepository.getRestaurantsCreatedAtByIds([restaurantId]);
            assert(
                restaurantWithCreatedAt[0]?.createdAt,
                '[GetAggregatedInsightsForRestaurantAndPlatformUseCase] restaurant createdAt is not defined'
            );
            filtersInput.comparisonPeriodData = {
                comparisonPeriod,
                restaurantStartDate: restaurantWithCreatedAt[0].createdAt,
            };
        }

        const filters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(platformKey, filtersInput);

        return getPlatformInsights(platformKey)?.getInsightsAggregated(
            restaurantId,
            metrics,
            aggregators,
            filters,
            isAggregatedSocialMediaPerformanceReleaseEnabled
        );
    }
}
