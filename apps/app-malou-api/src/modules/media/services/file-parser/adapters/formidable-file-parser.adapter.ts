import { Request } from 'express';
import formidable from 'formidable';
import fs from 'fs/promises';
import { singleton } from 'tsyringe';

import { getTypeFromExtension, getTypeFromMimetype } from '@malou-io/package-utils';

import { Config } from ':config';
import { getMimetypeFromExtension } from ':helpers/utils';
import { FileMetadata, FileParser, ParsedFile } from ':modules/media/services/file-parser/file-parser.interface';
import { SharpImageResizer } from ':modules/media/services/image-resizers/adapters/sharp-image-resizer.adapter';

@singleton()
export class FormidableFileParser implements FileParser {
    constructor(private readonly _sharp: SharpImageResizer) {}
    parseFiles(request: Request, filesFieldNameFromForm: string): Promise<ParsedFile[]> {
        const form = formidable({
            multiples: true,
            uploadDir: './downloadedMedias/',
            keepExtensions: true,
            maxFileSize: Number(Config.multer.limits.fileSize.video),
        });
        return new Promise((resolve, reject) => {
            form.parse(request, async (err, fields, files) => {
                try {
                    if (err) {
                        for (const file of files || []) {
                            if (file?.path) {
                                await fs.unlink(file.path);
                            }
                        }
                        reject(err);
                    }
                    const metadataList: FileMetadata[] = fields?.metadata ? JSON.parse(fields.metadata) : [];

                    const getExtensionFromFileName = (fileName: string): string | undefined => fileName.split('.')?.pop();
                    const filesFlattened: any = [files[filesFieldNameFromForm]].flat().map(async (file, index) => {
                        const metadata = metadataList[index];
                        const result: any = {
                            mimetype:
                                file.type === 'application/octet-stream'
                                    ? getMimetypeFromExtension(getExtensionFromFileName(file.name) ?? '')
                                    : file.type,
                            pathWhereFileIsStored: file.path,
                            name: file.name,
                            type:
                                file.type === 'application/octet-stream'
                                    ? getTypeFromExtension(file.name.split('.')?.pop())
                                    : getTypeFromMimetype(file.type),
                            title: metadata?.title,
                            description: metadata?.description,
                            originalMediaId: metadata?.originalMediaId,
                            category: metadata?.category,
                            restaurantId: metadata?.restaurantId,
                            userId: metadata?.userId,
                        };
                        if (metadata?.width && metadata?.height) {
                            const height = parseFloat(metadata.height);
                            const width = parseFloat(metadata.width);
                            result.dimensions = {
                                original: {
                                    width,
                                    height,
                                },
                            };
                        } else {
                            const dimensions = await this._sharp
                                .initialize(file.path, '')
                                .metadata()
                                .then((met) => ({
                                    width: met.width,
                                    height: met.height,
                                }));
                            result.dimensions = {
                                original: {
                                    width: dimensions.width,
                                    height: dimensions.height,
                                },
                            };
                        }

                        return result;
                    });
                    resolve(await Promise.all(filesFlattened));
                } catch (error) {
                    reject(error);
                }
            });
        });
    }
}
