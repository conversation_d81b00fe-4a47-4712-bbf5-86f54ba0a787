import { singleton } from 'tsyringe';

import { InformationUpdatePlatformStateStatus } from '@malou-io/package-utils';

import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { logger } from ':helpers/logger';
import { CreateInfoUpdateErrorNotificationProducer } from ':modules/notifications/queues/create-info-update-error-notification/create-info-update-error-notification.producer';
import { StartUpdateOnPlatformsUseCase } from ':modules/platforms/use-cases/start-update-on-platforms/start-update-on-platforms.use-case';

import { InformationUpdatesRepository } from './information-updates.repository';

@singleton()
export default class PublishInformationUpdatesUseCase {
    constructor(
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _startUpdateOnPlatformsUseCase: StartUpdateOnPlatformsUseCase,
        private readonly _createInfoUpdateErrorNotificationProducer: CreateInfoUpdateErrorNotificationProducer,
        private readonly _asyncLocalStorageService: AsyncLocalStorageService
    ) {}

    async execute(): Promise<void> {
        const informationUpdatesToPublish = await this._informationUpdatesRepository.getInformationUpdatesToPublish();

        await Promise.all(
            informationUpdatesToPublish.map((informationUpdateToPublish) =>
                this._asyncLocalStorageService.createStoreAndRun(
                    { restaurant: { id: informationUpdateToPublish.restaurantId.toString() } },
                    async () => {
                        const { platformStates } = await this._startUpdateOnPlatformsUseCase.execute(informationUpdateToPublish);

                        await this._informationUpdatesRepository.setInformationUpdateAsValidatedAndSavePlatformStates(
                            informationUpdateToPublish._id,
                            platformStates
                        );

                        if (
                            platformStates.some((platformState) =>
                                [
                                    InformationUpdatePlatformStateStatus.ERROR,
                                    InformationUpdatePlatformStateStatus.BAD_ACCESS,
                                    InformationUpdatePlatformStateStatus.INVALID_PAGE,
                                    InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE,
                                ].includes(platformState.status)
                            )
                        ) {
                            this._createInfoUpdateErrorNotificationProducer
                                .sendMessage({
                                    infoUpdateId: informationUpdateToPublish._id.toString(),
                                })
                                .catch((error) => {
                                    logger.error(`Error while sending info update error notification: ${error}`);
                                });
                        }
                    }
                )
            )
        );
    }
}
