import { singleton } from 'tsyringe';

import { IInformationUpdate, IRestaurant } from '@malou-io/package-models';
import {
    getPlatformDefinition,
    InformationUpdateProvider,
    isNotNil,
    PlatformKey,
    platformKeyToProvider,
    platformKeyToSupportedPlatformKey,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GetSupportedFieldsForPlatformService } from ':modules/information-updates/services/get-supported-fields-for-platform.service';

@singleton()
export class GetMalouPlatformKeysToUpdateService {
    constructor(private readonly _getSupportedFieldsForPlatformService: GetSupportedFieldsForPlatformService) {}

    execute({
        restaurant,
        informationUpdateData,
        filterPlatformKey,
    }: {
        restaurant: IRestaurant;
        informationUpdateData: IInformationUpdate['data'];
        filterPlatformKey?: (platformKey?: PlatformKey) => boolean;
    }): PlatformKey[] {
        const shouldKeepPlatformKey = filterPlatformKey ?? (() => true);

        const platformKeys = restaurant.access
            .filter((access) => access.active && getPlatformDefinition(access.platformKey)?.shouldCompareInformation)
            .map((access) => access.platformKey);

        if (restaurant.appleBusinessConnect?.locationId) {
            platformKeys.push(PlatformKey.ABC);
        }

        const filteredPlatformKeys = platformKeys
            .filter((platformKey) => isNotNil(platformKeyToSupportedPlatformKey[platformKey]))
            .filter(shouldKeepPlatformKey)
            .filter((platformKey) => {
                const provider = InformationUpdateProvider.MALOU;
                const providerKey = platformKeyToProvider[provider][platformKey];
                const willUpdateData = this._getSupportedFieldsForPlatformService.willUpdateData({
                    data: informationUpdateData,
                    providerKey,
                    provider,
                });

                if (!willUpdateData) {
                    logger.info(`[PUBLISH INFOS] Malou platform will not update any data`, {
                        restaurantId: restaurant._id,
                        data: informationUpdateData,
                        platformKey,
                    });
                }

                return willUpdateData;
            });

        return filteredPlatformKeys;
    }
}
