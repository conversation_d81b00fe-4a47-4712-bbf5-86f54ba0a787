import { singleton } from 'tsyringe';

import { NfcDto, UpdateStickerBodyDto } from '@malou-io/package-dto';

import { StickersMapper } from ':modules/nfc/stickers/stickers.mapper';
import { StickersRepository } from ':modules/nfc/stickers/stickers.repository';

@singleton()
export class UpdateStickerUseCase {
    constructor(
        private readonly _stickersMapper: StickersMapper,
        private readonly _stickersRepository: StickersRepository
    ) {}

    async execute(id: string, data: UpdateStickerBodyDto): Promise<NfcDto> {
        const stickerToUpdate = this._stickersMapper.toUpdateSticker(id, data);
        const sticker = await this._stickersRepository.updateSticker(id, stickerToUpdate);
        return sticker.toNfcDto();
    }
}
