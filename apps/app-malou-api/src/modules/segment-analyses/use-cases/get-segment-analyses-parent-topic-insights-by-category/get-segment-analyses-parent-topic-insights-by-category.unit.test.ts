import { cloneDeep } from 'lodash';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { MalouComparisonPeriod, PlatformKey, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { GetSegmentAnalysisParentTopicInsightsByCategoryUseCase } from ':modules/segment-analyses/use-cases/get-segment-analyses-parent-topic-insights-by-category/get-segment-analyses-parent-topic-insights-by-category.use-case';
import { DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY } from ':modules/segment-analyses/use-cases/get-segment-analyses-parent-topic-insights-by-category/get-segment-analyses-parent-topic-insights.contant';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';

let getSegmentAnalysisParentTopicInsightsByCategoryUseCase: GetSegmentAnalysisParentTopicInsightsByCategoryUseCase;

describe('GetSegmentAnalysisParentTopicInsightsByCategoryUseCase', () => {
    beforeAll(() => {
        container.clearInstances();

        registerRepositories(['RestaurantsRepository', 'SegmentAnalysisParentTopicsRepository', 'SegmentAnalysesRepository']);

        getSegmentAnalysisParentTopicInsightsByCategoryUseCase = container.resolve(GetSegmentAnalysisParentTopicInsightsByCategoryUseCase);
    });

    describe('execute', () => {
        it('should return empty insights if there is no parents topic', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [];
                        },
                    },
                    segmentAnalyses: {
                        data() {
                            return [getDefaultSegmentAnalysis().build()];
                        },
                    },
                },
                expectedResult(_dependencies): any {
                    return DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY;
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should only return parent topic insights for selected restaurants', async () => {
            const restaurantId1 = newDbId();
            const restaurantId2 = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()._id(restaurantId1).createdAt(createdAtDate).build(),
                                getDefaultRestaurant()._id(restaurantId2).createdAt(createdAtDate).build(),
                            ];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .name('Lenteurs')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId2')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId1.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should only count reviews within time range', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Lenteurs')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(new Date('2025-02-05'))
                                    .reviewSocialId('socialId2')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                        [ReviewAnalysisTag.SERVICE]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                    name: 'Lenteurs',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Lenteurs',
                                        es: 'Lenteurs',
                                        fr: 'Lenteurs',
                                        it: 'Lenteurs',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should be able to compute multiple parent topic insights with same category', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Galettes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Lenteurs')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(new Date('2025-02-05'))
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId2')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[2]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(new Date('2025-02-05'))
                                    .reviewSocialId('socialId3')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 2,
                            totalPositiveCount: 1,
                            totalNegativeCount: 1,
                            totalPositiveCountEvolution: 1,
                            totalNegativeCountEvolution: 1,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                    name: 'Galettes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 1,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 1,
                                    translations: {
                                        en: 'Galettes',
                                        es: 'Galettes',
                                        fr: 'Galettes',
                                        it: 'Galettes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                        [ReviewAnalysisTag.SERVICE]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[2]._id.toString(),
                                    name: 'Lenteurs',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Lenteurs',
                                        es: 'Lenteurs',
                                        fr: 'Lenteurs',
                                        it: 'Lenteurs',
                                    },
                                    createdAt: new Date('2025-02-05'),
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return new parentTopic if created 1 week ago', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');
            const createdAtDate2 = new Date('2025-01-28');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate2)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Lenteurs')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(new Date('2025-02-05'))
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate2)
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(new Date('2025-02-05'))
                                    .reviewSocialId('socialId2')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: true,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate2,
                                },
                            ],
                        },
                        [ReviewAnalysisTag.SERVICE]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                    name: 'Lenteurs',
                                    isNew: true,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Lenteurs',
                                        es: 'Lenteurs',
                                        fr: 'Lenteurs',
                                        it: 'Lenteurs',
                                    },
                                    createdAt: new Date('2025-02-05'),
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-02-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return parentTopic with negative evolutionCount if less segments than other period', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(new Date('2025-01-05'))
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Lenteurs')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(new Date('2025-01-05'))
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis() // Should be counted in previous period
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(new Date('2025-01-05'))
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis() // Should be counted in previous period
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .reviewSocialId('socialId2')
                                    .build(),
                                getDefaultSegmentAnalysis() // Should be counted as positiveCount
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(new Date('2025-02-08'))
                                    .reviewSocialId('socialId3')
                                    .build(),
                                getDefaultSegmentAnalysis() // Should not be match
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(new Date('2025-03-05'))
                                    .reviewSocialId('socialId4')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: -1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: -1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                        [ReviewAnalysisTag.SERVICE]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                    name: 'Lenteurs',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Lenteurs',
                                        es: 'Lenteurs',
                                        fr: 'Lenteurs',
                                        it: 'Lenteurs',
                                    },
                                    createdAt: new Date('2025-01-05'),
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-02-01'),
                endDate: new Date('2025-03-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return data with SINCE_START comparison period', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2024-12-10');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis() // should be match in countEvolution
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .topic('Crepes complete')
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .build(),
                                getDefaultSegmentAnalysis() // should be match in countEvolution
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .topic('Crepes suzette')
                                    .reviewSocialCreatedAt(new Date('2025-01-01'))
                                    .reviewSocialId('socialId2')
                                    .build(),
                                getDefaultSegmentAnalysis() // should not be match
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .topic('Crepes flambées')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .reviewSocialId('socialId3')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: -2,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: -2,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-02-01'),
                endDate: new Date('2025-03-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.SINCE_START,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return a segment without data if it is a favorite topic', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .isFavorite(true)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: true,
                                    isUserInput: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: { en: 'Crepes', es: 'Crepes', fr: 'Crepes', it: 'Crepes' },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should count segments as 1 by sentiment if it`s provided from the same review', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .segment('Très bonnes crepes')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .segment('Les meilleures crepes du monde')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should count segments as 1 by sentiment for evolution if it`s provided from the same review', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');
            const createdAtDate2 = new Date('2024-12-15');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Serveur')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .segment('Très bonnes crepes')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .segment('Les meilleures crepes du monde')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate2)
                                    .reviewSocialId('socialId2')
                                    .segment('Crêpes beurre sucre')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate2)
                                    .reviewSocialId('socialId2')
                                    .segment('Crepes nutella')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(createdAtDate2)
                                    .reviewSocialId('socialId2')
                                    .segment('Bon service')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .reviewSocialCreatedAt(createdAtDate2)
                                    .reviewSocialId('socialId2')
                                    .segment('Serveurs gentils')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 1,
                            totalPositiveCount: 1,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 1,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0, // 0 as the 2 segments are counted as 1 because it's the same review
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                        [ReviewAnalysisTag.SERVICE]: {
                            total: 0,
                            totalPositiveCount: 0,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: -1,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                    name: 'Serveur',
                                    isNew: false,
                                    isUserInput: false,
                                    isFavorite: false,
                                    positiveCount: 0,
                                    negativeCount: 0,
                                    positiveCountEvolution: -1,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Serveur',
                                        es: 'Serveur',
                                        fr: 'Serveur',
                                        it: 'Serveur',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2024-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should set evolution to 0 if we dont have all data for previous period', async () => {
            const restaurantId = newDbId();
            const createdAtDate = new Date('2025-01-05');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).createdAt(createdAtDate).build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(restaurantId)
                                    .name('Crepes')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .createdAt(createdAtDate)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId1')
                                    .segment('Très bonnes crepes')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId2')
                                    .segment('Crêpes beurre sucre')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .reviewSocialCreatedAt(createdAtDate)
                                    .reviewSocialId('socialId3')
                                    .segment('Crepes nutella')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        ...cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY),
                        [ReviewAnalysisTag.FOOD]: {
                            total: 3,
                            totalPositiveCount: 3,
                            totalNegativeCount: 0,
                            totalPositiveCountEvolution: 0,
                            totalNegativeCountEvolution: 0,
                            parentTopics: [
                                {
                                    parentTopicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                    name: 'Crepes',
                                    isNew: false,
                                    isFavorite: false,
                                    isUserInput: false,
                                    positiveCount: 3,
                                    negativeCount: 0,
                                    positiveCountEvolution: 0,
                                    negativeCountEvolution: 0,
                                    translations: {
                                        en: 'Crepes',
                                        es: 'Crepes',
                                        fr: 'Crepes',
                                        it: 'Crepes',
                                    },
                                    createdAt: createdAtDate,
                                },
                            ],
                        },
                    };
                },
            });

            await testCase.build();
            const result = await getSegmentAnalysisParentTopicInsightsByCategoryUseCase.execute(new Date('2025-03-01'), {
                startDate: new Date('2025-01-01'),
                endDate: new Date('2025-02-01'),
                newSemanticAnalysisStartDate: new Date('2025-01-01'),
                restaurantId: restaurantId.toString(),
                keys: [PlatformKey.GMB],
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });
    });
});
