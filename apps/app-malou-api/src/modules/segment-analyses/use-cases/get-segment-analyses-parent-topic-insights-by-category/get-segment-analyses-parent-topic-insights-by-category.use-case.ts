import { cloneDeep, groupBy, uniqBy } from 'lodash';
import { singleton } from 'tsyringe';

import { GetSegmentAnalysisParentTopicInsightsByCategoryResponseDto } from '@malou-io/package-dto';
import { ISegmentAnalysis, toDbIds } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    EMPTY_KEY,
    getDateRangeFromMalouComparisonPeriod,
    isNotNil,
    MalouComparisonPeriod,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisTag,
} from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SegmentAnalysesReviewCountWithParentTopicIds } from ':modules/segment-analyses/segment-analyses.interface';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY } from ':modules/segment-analyses/use-cases/get-segment-analyses-parent-topic-insights-by-category/get-segment-analyses-parent-topic-insights.contant';
import { SegmentAnalysisParentTopicByCategory } from ':modules/segment-analyses/use-cases/get-segment-analyses-parent-topic-insights-by-category/segment-analyses-parent-topic-insights-by-category';
import { SegmentAnalysisParentTopic } from ':modules/segment-analysis-parent-topics/entities/segment-analysis-parent-topic.entity';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

interface GetSegmentAnalysisParentTopicInsightsByCategoryFilters {
    startDate: Date;
    endDate: Date;
    newSemanticAnalysisStartDate: Date;
    restaurantId: string;
    keys: PlatformKey[];
    comparisonPeriod?: MalouComparisonPeriod;
}

@singleton()
export class GetSegmentAnalysisParentTopicInsightsByCategoryUseCase {
    constructor(
        private readonly _segmentAnalysesParentTopicsRepository: SegmentAnalysisParentTopicsRepository,
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(
        currentDate: Date,
        filters: GetSegmentAnalysisParentTopicInsightsByCategoryFilters
    ): Promise<GetSegmentAnalysisParentTopicInsightsByCategoryResponseDto> {
        const parentTopics = await this._segmentAnalysesParentTopicsRepository.getByRestaurantIds([filters.restaurantId]);

        if (parentTopics.length === 0) {
            return cloneDeep(DEFAULT_SEGMENT_ANALYSES_PARENT_TOPIC_INSIGHTS_BY_CATEGORY);
        }

        const { startDate: comparisonStartDate, endDate: comparisonEndDate } = await this._getComparisonDates(filters);

        const segmentAnalysesParentTopicByCategory = new SegmentAnalysisParentTopicByCategory({
            startDate: comparisonStartDate ?? filters.newSemanticAnalysisStartDate,
            newSemanticAnalysisStartDate: filters.newSemanticAnalysisStartDate,
        });
        const segmentAnalysesWithEvolutionByUniqueKey = await this._getSegmentAnalysesWithComparisonCount(parentTopics, filters, {
            comparisonStartDate,
            comparisonEndDate,
        });

        const segmentAnalysesParentTopicInsights = parentTopics
            .map((parentTopic) => {
                const positiveUniqueKey = this._computeUniqueMapKey({
                    parentTopicId: parentTopic.id,
                    sentiment: ReviewAnalysisSentiment.POSITIVE,
                });
                const positiveDataForUniqueKey = segmentAnalysesWithEvolutionByUniqueKey[positiveUniqueKey];
                const positiveSegments = positiveDataForUniqueKey?.segments;
                const positiveComparisonSegments = positiveDataForUniqueKey?.comparisonSegmentCount;

                const negativeUniqueKey = this._computeUniqueMapKey({
                    parentTopicId: parentTopic.id,
                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                });
                const negativeDataForUniqueKey = segmentAnalysesWithEvolutionByUniqueKey[negativeUniqueKey];
                const negativeSegments = negativeDataForUniqueKey?.segments;
                const negativeComparisonSegments = negativeDataForUniqueKey?.comparisonSegmentCount;

                return {
                    category: parentTopic.category,
                    data: {
                        parentTopicId: parentTopic.id,
                        name: parentTopic.name,
                        translations: {
                            [ApplicationLanguage.FR]: parentTopic.translations?.getTranslation(ApplicationLanguage.FR) ?? parentTopic.name,
                            [ApplicationLanguage.EN]: parentTopic.translations?.getTranslation(ApplicationLanguage.EN) ?? parentTopic.name,
                            [ApplicationLanguage.ES]: parentTopic.translations?.getTranslation(ApplicationLanguage.ES) ?? parentTopic.name,
                            [ApplicationLanguage.IT]: parentTopic.translations?.getTranslation(ApplicationLanguage.IT) ?? parentTopic.name,
                        },
                        isNew: parentTopic.isNewTopic(currentDate),
                        isFavorite: parentTopic.isFavorite,
                        isUserInput: parentTopic.isUserInput ?? false,
                        createdAt: parentTopic.createdAt,
                        positiveSegmentReviewSocialIds: positiveSegments?.map((segment) => segment.reviewSocialId) ?? [],
                        positiveComparisonSegmentReviewSocialIds:
                            positiveComparisonSegments?.flatMap((segment) => segment.reviewSocialIds) ?? [],
                        negativeSegmentReviewSocialIds: negativeSegments?.map((segment) => segment.reviewSocialId) ?? [],
                        negativeComparisonSegmentReviewSocialIds:
                            negativeComparisonSegments?.flatMap((segment) => segment.reviewSocialIds) ?? [],
                    },
                };
            })
            .filter(isNotNil);

        segmentAnalysesParentTopicInsights.forEach((segmentAnalysesParentTopicInsight) => {
            segmentAnalysesParentTopicByCategory.insertSegmentAnalysisParentTopicInsights(
                segmentAnalysesParentTopicInsight.category,
                segmentAnalysesParentTopicInsight.data
            );
        });
        segmentAnalysesParentTopicByCategory.addMissingCategories();
        segmentAnalysesParentTopicByCategory.computeTotalCount();
        return segmentAnalysesParentTopicByCategory.toDto();
    }

    private _computeUniqueMapKey(segment: { parentTopicId: string; sentiment: ReviewAnalysisSentiment }): string {
        return `${segment.parentTopicId}-${segment.sentiment}`;
    }

    private async _getSegmentAnalysesWithComparisonCount(
        parentTopics: SegmentAnalysisParentTopic[],
        { restaurantId, keys, startDate, endDate }: GetSegmentAnalysisParentTopicInsightsByCategoryFilters,
        { comparisonStartDate, comparisonEndDate }: { comparisonStartDate: Date | null; comparisonEndDate: Date | null }
    ): Promise<Record<string, { segments: ISegmentAnalysis[]; comparisonSegmentCount: SegmentAnalysesReviewCountWithParentTopicIds[] }>> {
        const segmentAnalysesByUniqueKey = await this._getSegmentAnalysesByUniqueKey(parentTopics, {
            keys,
            startDate,
            endDate,
        });
        const comparisonSegmentCountByUniqueKey = await this._getComparisonSegmentCount(parentTopics, restaurantId, {
            comparisonStartDate,
            comparisonEndDate,
        });
        return parentTopics.reduce((acc, parentTopic) => {
            const positiveUniqueKey = this._computeUniqueMapKey({
                parentTopicId: parentTopic.id,
                sentiment: ReviewAnalysisSentiment.POSITIVE,
            });
            acc[positiveUniqueKey] = {
                segments: segmentAnalysesByUniqueKey[positiveUniqueKey] || [],
                comparisonSegmentCount: comparisonSegmentCountByUniqueKey[positiveUniqueKey] || [],
            };

            const negativeUniqueKey = this._computeUniqueMapKey({
                parentTopicId: parentTopic.id,
                sentiment: ReviewAnalysisSentiment.NEGATIVE,
            });
            acc[negativeUniqueKey] = {
                segments: segmentAnalysesByUniqueKey[negativeUniqueKey] || [],
                comparisonSegmentCount: comparisonSegmentCountByUniqueKey[negativeUniqueKey] || [],
            };
            return acc;
        }, {});
    }

    private async _getSegmentAnalysesByUniqueKey(
        parentTopics: SegmentAnalysisParentTopic[],
        { keys, startDate, endDate }: Pick<GetSegmentAnalysisParentTopicInsightsByCategoryFilters, 'keys' | 'startDate' | 'endDate'>
    ): Promise<Record<string, ISegmentAnalysis[]>> {
        const parentTopicIds = parentTopics.map((parentTopic) => parentTopic.id);
        const segmentAnalyses = await this._segmentAnalysesRepository.find({
            filter: {
                segmentAnalysisParentTopicIds: { $in: toDbIds(parentTopicIds) },
                platformKey: { $in: keys },
                reviewSocialCreatedAt: { $gte: startDate, $lte: endDate },
                category: { $ne: ReviewAnalysisTag.OVERALL_EXPERIENCE },
                sentiment: { $ne: ReviewAnalysisSentiment.NEUTRAL },
            },
            options: {
                lean: true,
            },
        });

        const filterUniqSegmentAnalysesByReview = uniqBy(
            segmentAnalyses,
            (segmentAnalysis) => `${segmentAnalysis.reviewSocialId}-${segmentAnalysis.category}-${segmentAnalysis.sentiment}`
        );
        return Object.fromEntries(
            Object.entries(
                groupBy(filterUniqSegmentAnalysesByReview, (segmentAnalysis) => {
                    const parentTopicId = segmentAnalysis.segmentAnalysisParentTopicIds
                        ?.find((id) => parentTopicIds.includes(id.toString()))
                        ?.toString();

                    return parentTopicId
                        ? this._computeUniqueMapKey({
                              parentTopicId,
                              sentiment: segmentAnalysis.sentiment,
                          })
                        : EMPTY_KEY;
                })
            ).filter(([key]) => !key.includes(EMPTY_KEY))
        );
    }

    private async _getComparisonSegmentCount(
        parentTopics: SegmentAnalysisParentTopic[],
        restaurantId: string,
        { comparisonStartDate, comparisonEndDate }: { comparisonStartDate: Date | null; comparisonEndDate: Date | null }
    ): Promise<Record<string, SegmentAnalysesReviewCountWithParentTopicIds[]>> {
        const parentTopicIds = parentTopics.filter(({ restaurantId: restId }) => restaurantId === restId).map(({ id }) => id);

        const segmentCounts = await this._segmentAnalysesRepository.getSegmentAnalysesReviewCountBySentiment({
            segmentAnalysisParentTopicIds: { $in: toDbIds(parentTopicIds) },
            reviewSocialCreatedAt: { $gte: comparisonStartDate, $lte: comparisonEndDate },
        });
        return groupBy(segmentCounts, (segmentCount) =>
            this._computeUniqueMapKey({
                parentTopicId: segmentCount.segmentAnalysisParentTopicId,
                sentiment: segmentCount.sentiment,
            })
        );
    }

    private async _getComparisonDates({
        restaurantId,
        startDate,
        endDate,
        comparisonPeriod,
    }: Pick<
        GetSegmentAnalysisParentTopicInsightsByCategoryFilters,
        'restaurantId' | 'startDate' | 'endDate' | 'comparisonPeriod'
    >): Promise<{ startDate: Date | null; endDate: Date | null }> {
        const restaurantsWithCreatedAt = await this._restaurantsRepository.getRestaurantsCreatedAtByIds([restaurantId]);
        const createdAt = restaurantsWithCreatedAt[0]?.createdAt;
        return getDateRangeFromMalouComparisonPeriod({
            restaurantStartDate: createdAt,
            dateFilters: { startDate, endDate },
            comparisonPeriod: comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD,
        });
    }
}
