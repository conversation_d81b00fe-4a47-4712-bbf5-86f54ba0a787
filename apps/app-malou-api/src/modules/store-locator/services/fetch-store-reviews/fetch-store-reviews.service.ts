import { fromBuff<PERSON> } from 'file-type';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageReviewsBlockValidator } from '@malou-io/package-dto';
import { IReview, IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { fetchImage } from ':helpers/fetch-image-from-remote';
import { logger } from ':helpers/logger';
import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

interface ReviewFromDB extends Pick<IReview, '_id' | 'reviewer' | 'text' | 'rating' | 'key' | 'socialSortDate'> {}
type StoreReview = NonNullable<GetStoreLocatorStorePageDto['reviewsBlock']>['reviews'][0];

@singleton()
export class FetchStoreLocatorReviewsBlockService {
    private readonly _MINIMUM_REVIEWS_COUNT = 8;
    private readonly _REVIEWS_MAX_AGE_IN_DAYS = 60;
    private readonly _REVIEWS_CONTENT_MIN_LENGTH = 130;

    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute({
        restaurantId,
        storeLocatorRestaurantPage,
    }: {
        restaurantId: string;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['reviewsBlock'] | undefined }> {
        try {
            const reviews = await this._getReviews({ restaurantId, storeLocatorRestaurantPage });

            const reviewsBlock = {
                title: storeLocatorRestaurantPage.blocks.reviews.title.toUpperCase(),
                reviews,
                cta: storeLocatorRestaurantPage.blocks.reviews.cta,
            };
            const parsedReviewsBlock = await storeLocatorStorePageReviewsBlockValidator.parseAsync(reviewsBlock);

            logger.info('[STORE_LOCATOR] [Reviews block] Reviews block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.reviews.backup': parsedReviewsBlock },
            });

            return { success: true, data: parsedReviewsBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Reviews block] Failed to fetch store information, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.reviews?.backup) {
                try {
                    const reviewsBlock = storeLocatorRestaurantPage.blocks.reviews.backup;
                    const parsedReviewsBlock = await storeLocatorStorePageReviewsBlockValidator.parseAsync(reviewsBlock);

                    return { success: false, data: parsedReviewsBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Reviews block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    private async _getReviews({
        restaurantId,
        storeLocatorRestaurantPage,
    }: {
        restaurantId: string;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<StoreReview[]> {
        const reviews = await this._reviewsRepository.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: PlatformKey.GMB,
                rating: 5,
                reviewer: { $exists: true },
                lang: storeLocatorRestaurantPage.lang,
                text: { $exists: true, $ne: '' },
                $expr: { $gt: [{ $strLenCP: { $ifNull: ['$text', ''] } }, this._REVIEWS_CONTENT_MIN_LENGTH] },
            },
            projection: { _id: 1, reviewer: 1, text: 1, rating: 1, key: 1, socialSortDate: 1 },
            options: { sort: { socialSortDate: -1 }, limit: this._MINIMUM_REVIEWS_COUNT, lean: true },
        });

        if (reviews.length < this._MINIMUM_REVIEWS_COUNT) {
            throw new MalouError(MalouErrorCode.STORE_LOCATOR_REVIEWS_COUNT_INSUFFICIENT, { metadata: { restaurantId } });
        }

        const reviewerProfilePictureProperties = await this._handleReviewerProfilePictures({
            reviews,
            restaurantId,
            organizationId: storeLocatorRestaurantPage.organizationId.toString(),
        });

        // Check if one of the reviews is older than 30 days
        const shouldDisplayPublishedAt = reviews.every((review) => {
            const reviewDate = DateTime.fromJSDate(new Date(review.socialSortDate));
            return DateTime.now().diff(reviewDate, 'days').days <= this._REVIEWS_MAX_AGE_IN_DAYS;
        });

        return filterByRequiredKeys(reviews, ['text', 'rating', 'reviewer']).map((review) => ({
            picture: reviewerProfilePictureProperties[review._id.toString()],
            starsCount: review.rating,
            platformKey: review.key,
            ...(shouldDisplayPublishedAt && {
                publishedAt: review.socialSortDate.toLocaleDateString('fr-FR', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                }),
            }),
            userName: this._capitalizeUserName(review.reviewer.displayName),
            content: review.text,
        }));
    }

    private async _handleReviewerProfilePictures({
        reviews,
        restaurantId,
        organizationId,
    }: {
        reviews: ReviewFromDB[];
        restaurantId: string;
        organizationId: string;
    }): Promise<Record<string, StoreReview['picture']>> {
        // Clear profile pictures folder to avoid infinite growth
        try {
            await this._cloudStorageService.emptyDirectory(
                `store-locator/organization/${organizationId}/restaurants/${restaurantId}/reviews/profile-pictures`
            );
        } catch (err) {
            logger.warn('[STORE_LOCATOR] [Reviews block] Failed to empty profile pictures folder', { err });
        }

        const profilePictures = {};
        await Promise.all(
            reviews.map(async (review) => {
                profilePictures[review._id.toString()] = await this._handleReviewerPicture({ review, restaurantId, organizationId });
            })
        );

        return profilePictures;
    }

    private async _handleReviewerPicture({
        restaurantId,
        organizationId,
        review,
    }: {
        restaurantId: string;
        organizationId: string;
        review: ReviewFromDB;
    }): Promise<StoreReview['picture']> {
        try {
            const pictureUrl = review?.reviewer?.profilePhotoUrl;
            if (!pictureUrl) {
                return this._getDefaultPictureBackground({ review });
            }

            const imageBuffer = await fetchImage(pictureUrl);
            if (!imageBuffer) {
                return this._getDefaultPictureBackground({ review });
            }

            const fileTypeResult = await fromBuffer(imageBuffer);
            if (!fileTypeResult) {
                return this._getDefaultPictureBackground({ review });
            }

            const { ext, mime } = fileTypeResult;
            const s3Key = `store-locator/organization/${organizationId}/restaurants/${restaurantId}/reviews/profile-pictures/${review._id.toString()}.${ext}`;
            const uploadedUrl = await this._cloudStorageService.uploadBuffer({ buffer: imageBuffer, fileKey: s3Key, mimeType: mime });

            return { url: uploadedUrl };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Reviews block] Error handling reviewer picture', { err });
            return this._getDefaultPictureBackground({ review });
        }
    }

    private _getDefaultPictureBackground({ review }: { review: ReviewFromDB }): { initials: string; color: string } {
        const colors = [
            '#8B5E3C', // Warm Brown
            '#556B2F', // Dark Olive Green
            '#1F487E', // Deep Blue
            '#622569', // Dark Purple
            '#9A3B3B', // Muted Red
            '#2C3E50', // Charcoal Blue
            '#4E342E', // Dark Wood
            '#5D6D7E', // Steel Gray
            '#3E2723', // Espresso
            '#34495E', // Navy Gray
        ];
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const randomLetter = alphabet[Math.floor(Math.random() * alphabet.length)];

        const initials = review.reviewer?.displayName?.split(' ')?.[0]?.charAt(0)?.toUpperCase() || randomLetter;
        const color = colors[initials.charCodeAt(0) % colors.length];

        return {
            initials,
            color,
        };
    }

    private _capitalizeUserName(userName: string): string {
        return userName
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }
}
