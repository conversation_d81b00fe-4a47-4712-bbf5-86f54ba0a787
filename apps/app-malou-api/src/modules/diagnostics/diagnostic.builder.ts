import { Builder } from 'builder-pattern';
import crypto from 'node:crypto';

import { IDiagnostic, newDbId } from '@malou-io/package-models';
import { CountryCode, Locale } from '@malou-io/package-utils';

type DiagnosticPayload = IDiagnostic;

const _buildDiagnostic = (diagnostic: DiagnosticPayload) => Builder<DiagnosticPayload>(diagnostic);

export const getDefaultDiagnostic = () =>
    _buildDiagnostic({
        _id: newDbId(),
        malouDiagnosticId: crypto.randomUUID(),
        createdAt: new Date(),
        placeId: 'ChIJN1t_t2ZIw4RkM3NVg81p8Q',
        restaurantId: newDbId(),
        restaurant: {
            address: {
                locality: '80 Rue Paul Bert',
                regionCode: CountryCode.FRANCE,
                country: 'Lyon',
                postalCode: '69003',
            },
            name: 'Master Tacos',
            latlng: {
                lat: 45.756897,
                lng: 4.8509153,
            },
            categoryId: newDbId(),
            types: [],
            services: {
                acceptsDebitCards: true,
            },
        },
        events: [],
        isComplete: true,
        language: Locale.FR,
        similarRestaurantIds: [],
        photoCount: 0,
        reviewCount: 0,
    });
