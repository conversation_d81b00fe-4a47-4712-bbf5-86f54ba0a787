import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    CreateDiagnosticBodyDto,
    createDiagnosticBodyValidator,
    CreateDiagnosticParamsDto,
    createDiagnosticParamsValidator,
    DetectInconsistenciesParamsDto,
    detectInconsistenciesParamsValidator,
    DiagnosticDto,
    DiagnosticsWithRedirectionLinkDto,
    DiagnosticWithRedirectionLinkDto,
    GetDiagnosticByIdParamsDto,
    getDiagnosticByIdParamsValidator,
    GetDiagnosticsByEncryptedIdsBodyDto,
    getDiagnosticsByEncryptedIdsBodyValidator,
    GetDiagnosticsByIdsBodyDto,
    getDiagnosticsByIdsBodyValidator,
    GetDiagnosticsForEventParamsDto,
    getDiagnosticsForEventParamsValidator,
    GetInstagramPageParamsDto,
    getInstagramPageParamsValidator,
    TrackDiagnosticActionBodyDto,
    trackDiagnosticActionBodyValidator,
    UpdateDiagnosticEventBodyDto,
    updateDiagnosticEventBodyValidator,
    UpdateDiagnosticParamsDto,
    updateDiagnosticParamsValidator,
    UpdateDiagnosticWithInstagramBodyDto,
    updateDiagnosticWithInstagramBodyValidator,
    UpdateLocationCategoryBodyDto,
    updateLocationCategoryBodyValidator,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import StringEncryptorToSafeUrl from ':helpers/string-encryptor-to-safe-url/string-encryptor-to-safe-url';
import { InstagramPageDiscoveryService } from ':modules/diagnostics/services/get-instagram-page/instagram-page-discovery.service';
import { CreateOrRetrieveDiagnosticUseCase } from ':modules/diagnostics/use-cases/create-diagnostic/create-diagnostic.use-case';
import { UpdateDiagnosticWithInconsistenciesUseCase } from ':modules/diagnostics/use-cases/get-detected-inconsistencies.use-case';
import { GetDiagnosticByIdUseCase } from ':modules/diagnostics/use-cases/get-diagnostic-by-id.use-case';
import { GetDiagnosticsByIdsUseCase } from ':modules/diagnostics/use-cases/get-diagnostics-by-ids.use-case';
import { GetEventDiagnosticsUseCase } from ':modules/diagnostics/use-cases/get-event-diagnostics.use-case';
import { GetTodayDiagnosticsUseCase } from ':modules/diagnostics/use-cases/get-today-diagnostic.use-case';
import { HandleTrackDiagnosticActionUseCase } from ':modules/diagnostics/use-cases/track-diagnostic-action.use-case';
import { UpdateDiagnosticEventUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-event.use-cases';
import { UpdateDiagnosticWithReviewCountForSimilarRestaurantsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-average-review-count.use-case';
import { UpdateDiagnosticWithGoogleRatingUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-google-rating.use-case';
import { UpdateDiagnosticWithInstagramAccountUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-instagram-account.use-case';
import { UpdateDiagnosticWithInstagramRatingUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-instagram-rating.use-case';
import { UpdateDiagnosticWithKeywordsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-keywords.use-case';
import { UpdateDiagnosticWithReviewsAnalysesUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-reviews-analyses.use-case';
import { UpdateDiagnosticWithSimilarRestaurantsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-similar-restaurants.use-case';
import { UpdateRestaurantCategoryUseCase } from ':modules/diagnostics/use-cases/update-restaurant-category.use-case';
import { getPublicDiagnosticUrl } from ':modules/diagnostics/utilities/get-public-diagnostic-url';

@singleton()
export class DiagnosticController {
    constructor(
        private readonly _getDiagnosticByIdUseCase: GetDiagnosticByIdUseCase,
        private readonly _getDiagnosticsByIdsUseCase: GetDiagnosticsByIdsUseCase,
        private readonly _getTodayDiagnosticsUseCase: GetTodayDiagnosticsUseCase,
        private readonly _getEventDiagnosticsUseCase: GetEventDiagnosticsUseCase,
        private readonly _createOrRetrieveDiagnosticUseCase: CreateOrRetrieveDiagnosticUseCase,
        private readonly _updateDiagnosticWithInconsistenciesUseCase: UpdateDiagnosticWithInconsistenciesUseCase,
        private readonly _updateDiagnosticWithGoogleRatingUseCase: UpdateDiagnosticWithGoogleRatingUseCase,
        private readonly _updateDiagnosticWithSimilarRestaurantsUseCase: UpdateDiagnosticWithSimilarRestaurantsUseCase,
        private readonly _updateDiagnosticWithInstagramAccountUseCase: UpdateDiagnosticWithInstagramAccountUseCase,
        private readonly _instagramPageDiscoveryService: InstagramPageDiscoveryService,
        private readonly _updateDiagnosticWithInstagramRatingUseCase: UpdateDiagnosticWithInstagramRatingUseCase,
        private readonly _updateDiagnosticWithKeywordsUseCase: UpdateDiagnosticWithKeywordsUseCase,
        private readonly _updateDiagnosticWithReviewCountForSimilarRestaurantsUseCase: UpdateDiagnosticWithReviewCountForSimilarRestaurantsUseCase,
        private readonly _handleTrackDiagnosticActionUseCase: HandleTrackDiagnosticActionUseCase,
        private readonly _updateRestaurantCategoryUseCase: UpdateRestaurantCategoryUseCase,
        private readonly _updateDiagnosticWithReviewsAnalysesUseCase: UpdateDiagnosticWithReviewsAnalysesUseCase,
        private readonly _updateDiagnosticEventUseCase: UpdateDiagnosticEventUseCase
    ) {}

    @Params(getDiagnosticByIdParamsValidator)
    async handleGetDiagnosticById(req: Request<GetDiagnosticByIdParamsDto>, res: Response<ApiResultV2<DiagnosticDto>>, next: NextFunction) {
        try {
            const { diagnosticId } = req.params;
            const diagnostic = await this._getDiagnosticByIdUseCase.execute(diagnosticId);
            return res.json({ data: diagnostic });
        } catch (error) {
            next(error);
        }
    }

    @Params(getDiagnosticByIdParamsValidator)
    async handleGetDiagnosticWithRedirectionLinkById(
        req: Request<GetDiagnosticByIdParamsDto>,
        res: Response<ApiResultV2<DiagnosticWithRedirectionLinkDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const diagnostic = await this._getDiagnosticByIdUseCase.execute(diagnosticId);
            const publicDiagnosticUrl = getPublicDiagnosticUrl({
                diagnosticIds: [diagnosticId],
                asParam: true,
            });

            return res.json({ data: { diagnostic, publicDiagnosticUrl } });
        } catch (error) {
            next(error);
        }
    }

    @Body(getDiagnosticsByIdsBodyValidator)
    async handleGetDiagnosticsByIds(
        req: Request<any, any, GetDiagnosticsByIdsBodyDto>,
        res: Response<ApiResultV2<DiagnosticsWithRedirectionLinkDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticIds } = req.body;
            const result = await this._getDiagnosticsByIdsUseCase.execute(diagnosticIds);
            const publicDiagnosticUrl = getPublicDiagnosticUrl({
                diagnosticIds,
            });
            res.json({
                data: {
                    diagnostics: result,
                    publicDiagnosticUrl,
                },
            });
        } catch (error) {
            next(error);
        }
    }

    @Body(getDiagnosticsByEncryptedIdsBodyValidator)
    async handleGetDiagnosticsByEncryptedIds(
        req: Request<any, any, GetDiagnosticsByEncryptedIdsBodyDto>,
        res: Response<ApiResultV2<DiagnosticDto[]>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticIds } = req.body;
            const stringEncryptorToSafeUrl = new StringEncryptorToSafeUrl();
            const malouDiagnosticIds = diagnosticIds.map((encryptedDiagnosticId) =>
                stringEncryptorToSafeUrl.decryptSafeUrlToUuid(encryptedDiagnosticId)
            );
            const result = await this._getDiagnosticsByIdsUseCase.execute(malouDiagnosticIds);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(getDiagnosticByIdParamsValidator)
    async handleGetDiagnosticByEncryptedId(
        req: Request<GetDiagnosticByIdParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const malouDiagnosticId = new StringEncryptorToSafeUrl().decryptSafeUrlToUuid(diagnosticId);
            const diagnostic = await this._getDiagnosticByIdUseCase.execute(malouDiagnosticId);
            return res.json({ data: diagnostic });
        } catch (error) {
            next(error);
        }
    }

    @Params(getInstagramPageParamsValidator)
    async handleGetInstagramPage(req: Request<GetInstagramPageParamsDto>, res: Response, next: NextFunction) {
        try {
            const { text } = req.params;
            const result = await this._instagramPageDiscoveryService.getPage(text);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    async handleGetTodayDiagnostics(req: Request, res: Response, next: NextFunction) {
        try {
            const result = await this._getTodayDiagnosticsUseCase.execute();
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(getDiagnosticsForEventParamsValidator)
    async handleGetEventDiagnostics(
        req: Request<GetDiagnosticsForEventParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto[]>>,
        next: NextFunction
    ) {
        try {
            const { event } = req.params;
            const result = await this._getEventDiagnosticsUseCase.execute(event);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(createDiagnosticParamsValidator)
    @Body(createDiagnosticBodyValidator)
    async handleCreateOrRetrieveDiagnostic(
        req: Request<CreateDiagnosticParamsDto, any, CreateDiagnosticBodyDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { placeId } = req.params;
            const { lang } = req.body;
            const diagnostic = await this._createOrRetrieveDiagnosticUseCase.execute({
                placeId,
                lang,
            });
            return res.json({ data: diagnostic });
        } catch (error) {
            next(error);
        }
    }

    @Params(detectInconsistenciesParamsValidator)
    async handleUpdateDiagnosticWithInconsistencies(
        req: Request<DetectInconsistenciesParamsDto>,
        res: Response<ApiResultV2<any>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithInconsistenciesUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithSimilarRestaurant(
        req: Request<UpdateDiagnosticParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithSimilarRestaurantsUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithGoogleRating(
        req: Request<UpdateDiagnosticParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithGoogleRatingUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithKeywords(
        req: Request<UpdateDiagnosticParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithKeywordsUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithReviewCountForSimilarRestaurants(
        req: Request<UpdateDiagnosticParamsDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithReviewCountForSimilarRestaurantsUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    @Body(updateDiagnosticWithInstagramBodyValidator)
    async handleUpdateDiagnosticWithInstagramAccountDetails(
        req: Request<UpdateDiagnosticParamsDto, any, UpdateDiagnosticWithInstagramBodyDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const { instagramAccountName } = req.body;
            const result = await this._updateDiagnosticWithInstagramAccountUseCase.execute(diagnosticId, instagramAccountName);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithInstagramRating(
        req: Request<UpdateDiagnosticParamsDto, any, UpdateDiagnosticWithInstagramBodyDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithInstagramRatingUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    @Body(updateLocationCategoryBodyValidator)
    async handleUpdateRestaurantCategory(req: Request<any, any, UpdateLocationCategoryBodyDto>, res: Response, next: NextFunction) {
        try {
            const { diagnosticId } = req.params;
            const { categoryId } = req.body;
            const result = await this._updateRestaurantCategoryUseCase.execute(diagnosticId, categoryId);
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    async handleUpdateDiagnosticWithReviewsAnalysis(
        req: Request<UpdateDiagnosticParamsDto, any, UpdateDiagnosticWithInstagramBodyDto>,
        res: Response<ApiResultV2<DiagnosticDto>>,
        next: NextFunction
    ) {
        try {
            const { diagnosticId } = req.params;
            const result = await this._updateDiagnosticWithReviewsAnalysesUseCase.execute(diagnosticId);
            res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(updateDiagnosticParamsValidator)
    @Body(updateDiagnosticEventBodyValidator)
    async handleUpdateDiagnosticEvent(req: Request<any, any, UpdateDiagnosticEventBodyDto>, res: Response, next: NextFunction) {
        try {
            const { diagnosticId } = req.params;
            const { eventName } = req.body;
            if (!eventName) {
                return res.json({ data: 'ok' });
            }
            const result = await this._updateDiagnosticEventUseCase.execute(diagnosticId, eventName);
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(trackDiagnosticActionBodyValidator)
    handleTrackDiagnosticAction(req: Request<any, any, TrackDiagnosticActionBodyDto>, res: Response, next: NextFunction) {
        try {
            const { eventName, diagnosticIds, receiverEmail, identity } = req.body;
            this._handleTrackDiagnosticActionUseCase
                .execute({
                    identity,
                    eventName,
                    malouDiagnosticIds: diagnosticIds,
                    receiverEmail,
                    ip: req.ip,
                })
                .catch(logger.error);
            return res.json({ data: 'ok' });
        } catch (err) {
            next(err);
        }
    }
}
