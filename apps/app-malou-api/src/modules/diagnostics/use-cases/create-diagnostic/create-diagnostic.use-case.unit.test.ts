import { AxiosResponse } from 'axios';
import assert from 'node:assert/strict';
import crypto from 'node:crypto';
import { container } from 'tsyringe';

import { CountryCode, MaloupeLocale } from '@malou-io/package-utils';

import { registerOtherDependencies, registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCategory } from ':modules/categories/tests/categories.builder';
import { getDefaultDiagnostic } from ':modules/diagnostics/diagnostic.builder';
import { CreateOrRetrieveDiagnosticUseCase } from ':modules/diagnostics/use-cases/create-diagnostic/create-diagnostic.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GmapsProvider } from ':providers/google/gmaps.provider';
import { PlaceDetail } from ':providers/google/gmaps.provider.interface';
import { SlackAlertsService } from ':services/alerts/slack/slack-alerts.service';

const DEFAULT_RESTAURANT_CATEGORY_ID = 'gcid:restaurant';
const DEFAULT_PLACE_DETAIL = {
    id: crypto.randomUUID(),
    name: 'Test Restaurant',
    displayName: {
        text: 'Test Restaurant',
        languageCode: 'fr',
    },
    formattedAddress: '123 Test Street, Test City',
    location: { latitude: 45.756897, longitude: 4.8509153 },
    addressComponents: [
        { types: ['street_number'], longText: '123', shortText: '123', languageCode: 'fr' },
        { types: ['route'], longText: 'Rue du test', shortText: 'r. du test', languageCode: 'fr' },
        { types: ['postal_code'], longText: '69003', shortText: '69003', languageCode: 'fr' },
        { types: ['locality'], longText: 'Test ville', shortText: 'Test ville', languageCode: 'fr' },
        { types: ['country'], longText: 'France', shortText: CountryCode.FRANCE, languageCode: 'fr' },
    ],
    primaryType: 'restaurant',
    types: ['restaurant', 'food'],
    rating: 4.5,
    userRatingCount: 100,
    nationalPhoneNumber: '+33612345678',
    internationalPhoneNumber: '+33612345678',
    currentOpeningHours: {
        openNow: true,
        periods: [],
        weekdayDescriptions: [],
    },
    photos: [],
    reviews: [],
    accessibilityOptions: {
        wheelchairAccessibleParking: true,
        wheelchairAccessibleSeating: true,
    },
    allowsDogs: true,
    curbsidePickup: true,
    delivery: true,
    dineIn: true,
    goodForChildren: true,
    goodForGroups: true,
    goodForWatchingSports: true,
    liveMusic: true,
    menuForChildren: true,
    outdoorSeating: true,
    parkingOptions: {
        freeParkingLot: true,
        freeStreetParking: true,
        paidStreetParking: true,
    },
    paymentOptions: {
        acceptsCreditCards: true,
        acceptsDebitCards: true,
        acceptsCashOnly: true,
        acceptsNfc: true,
    },
    reservable: true,
    restroom: true,
    servesBeer: true,
    servesBreakfast: true,
    servesBrunch: true,
    servesCocktails: true,
    servesCoffee: true,
    servesDessert: true,
    servesDinner: true,
    servesLunch: true,
    servesVegetarianFood: true,
    servesWine: true,
    takeout: true,
};

class GmapsProviderMock implements Partial<GmapsProvider> {
    async getPlaceDetails(placeId: string): Promise<PlaceDetail> {
        return {
            ...DEFAULT_PLACE_DETAIL,
            id: placeId,
        };
    }
}

class SlackAlertsServiceMock implements Partial<SlackAlertsService> {
    async sendAlert(): Promise<{ success: boolean }> {
        return Promise.resolve({ success: true });
    }

    private async _sendSlackAlert(): Promise<void | AxiosResponse<{ ok: boolean }>> {
        return Promise.resolve({ data: { ok: true } } as AxiosResponse<{ ok: boolean }>);
    }
}

describe('CreateOrRetrieveDiagnosticUseCase', () => {
    let createDiagnosticUseCase: CreateOrRetrieveDiagnosticUseCase;

    beforeAll(() => {
        container.reset();

        registerOtherDependencies();
        registerRepositories(['CategoriesRepository', 'DiagnosticsRepository', 'RestaurantsRepository', 'PlatformsRepository']);

        container.register(GmapsProvider, { useValue: new GmapsProviderMock() as GmapsProvider });
        container.register(SlackAlertsService, { useValue: new SlackAlertsServiceMock() as unknown as SlackAlertsService });

        createDiagnosticUseCase = container.resolve(CreateOrRetrieveDiagnosticUseCase);
    });

    it('should creates a new diagnostic when none exists for the current month', async () => {
        const testCase = new TestCaseBuilderV2<'categories' | 'restaurants' | 'diagnostics' | 'platforms'>({
            seeds: {
                categories: {
                    data() {
                        return [getDefaultCategory().categoryId(DEFAULT_RESTAURANT_CATEGORY_ID).build()];
                    },
                },

                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                .name('Test Restaurant')
                                .latlng({ lat: 45.756897, lng: 4.8509153 })
                                .placeId(crypto.randomUUID())
                                .build(),
                        ];
                    },
                },

                diagnostics: {
                    data() {
                        return [];
                    },
                },

                platforms: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult: () => undefined,
        });
        await testCase.build();

        const { restaurants } = testCase.getSeededObjects();
        const restaurant = restaurants?.[0];
        assert(restaurant);

        const result = await createDiagnosticUseCase.execute({
            placeId: restaurant.placeId,
            lang: MaloupeLocale.FR,
        });

        expect(result).toBeDefined();
        expect(result.placeId).toBe(restaurant.placeId);
        expect(result.restaurant.name).toBe('Test Restaurant');
        expect(result.restaurant.address.country).toBe('France');
        expect(result.restaurant.address.postalCode).toBe('69003');
        expect(result.reviewCount).toBe(100);
        expect(result.restaurant.rating).toBe(4.5);
    });

    it('should returns existing diagnostic when one exists for the current month', async () => {
        const placeId = crypto.randomUUID();
        const testCase = new TestCaseBuilderV2<'categories' | 'restaurants' | 'diagnostics' | 'platforms'>({
            seeds: {
                categories: {
                    data() {
                        return [getDefaultCategory().categoryId(DEFAULT_RESTAURANT_CATEGORY_ID).build()];
                    },
                },

                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                .name('Test Restaurant')
                                .latlng({ lat: 45.756897, lng: 4.8509153 })
                                .placeId(placeId)
                                .build(),
                        ];
                    },
                },

                diagnostics: {
                    data(deps) {
                        return [getDefaultDiagnostic().restaurantId(deps.restaurants()[0]._id).build()];
                    },
                },

                platforms: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult: () => undefined,
        });
        await testCase.build();

        const result = await createDiagnosticUseCase.execute({
            placeId,
            lang: MaloupeLocale.FR,
        });

        expect(result).toBeDefined();
        expect(result.placeId).toBe(placeId);
        expect(result.restaurant.name).toBe('Test Restaurant');
        expect(result.createdAt.split('T')[0]).toBe(new Date().toISOString().split('T')[0]);
    });

    it('should compute street number and route from formatted address of not present in address components', async () => {
        class GmapsProviderMock2 implements Partial<GmapsProvider> {
            async getPlaceDetails(placeId: string): Promise<PlaceDetail> {
                return {
                    ...DEFAULT_PLACE_DETAIL,
                    id: placeId,
                    formattedAddress: '123 Rue du test, Test ville, 69003, France',
                    addressComponents: [
                        { types: ['postal_code'], longText: '69003', shortText: '69003', languageCode: 'fr' },
                        { types: ['locality'], longText: 'Test ville', shortText: 'Test ville', languageCode: 'fr' },
                        { types: ['country'], longText: 'France', shortText: CountryCode.FRANCE, languageCode: 'fr' },
                    ],
                };
            }
        }
        container.reset();

        registerOtherDependencies();
        registerRepositories(['CategoriesRepository', 'DiagnosticsRepository', 'RestaurantsRepository', 'PlatformsRepository']);

        container.register(GmapsProvider, { useValue: new GmapsProviderMock2() as GmapsProvider });
        container.register(SlackAlertsService, { useValue: new SlackAlertsServiceMock() as unknown as SlackAlertsService });

        createDiagnosticUseCase = container.resolve(CreateOrRetrieveDiagnosticUseCase);

        const placeId = crypto.randomUUID();
        const testCase = new TestCaseBuilderV2<'categories' | 'restaurants' | 'diagnostics' | 'platforms'>({
            seeds: {
                categories: {
                    data() {
                        return [getDefaultCategory().categoryId(DEFAULT_RESTAURANT_CATEGORY_ID).build()];
                    },
                },

                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                .name('Test Restaurant')
                                .latlng({ lat: 45.756897, lng: 4.8509153 })
                                .placeId(placeId)
                                .build(),
                        ];
                    },
                },

                diagnostics: {
                    data(deps) {
                        return [getDefaultDiagnostic().restaurantId(deps.restaurants()[0]._id).build()];
                    },
                },

                platforms: {
                    data() {
                        return [];
                    },
                },
            },
            expectedResult: () => undefined,
        });
        await testCase.build();

        const result = await createDiagnosticUseCase.execute({
            placeId,
            lang: MaloupeLocale.FR,
        });

        expect(result).toBeDefined();
        expect(result.restaurant.address.streetNumber).toBe('123');
        expect(result.restaurant.address.route).toBe('Rue du test');
    });
});
