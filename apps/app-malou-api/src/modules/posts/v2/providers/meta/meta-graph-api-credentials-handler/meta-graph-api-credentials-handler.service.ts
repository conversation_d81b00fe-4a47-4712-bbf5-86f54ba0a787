import { omit } from 'lodash';
import { err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { z, ZodType } from 'zod';

import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import {
    MetaGraphApiCredentialsHandlerErrorCodes as ApiErrorCodes,
    MetaGraphApiCredentialsHandlerError,
} from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import {
    MetaGraphApiError,
    MetaGraphApiRequestOptions,
} from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.definitions';
import { MetaGraphApiProvider } from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.provider';

const refreshPageAccessTokenValidator = z.object({
    access_token: z.string(),
});

/**
 * Like MetaGraphApiProvider, but does retries among credentials.
 * This class should not be used directly except by MetaGraphApiHelper. You should probably use MetaGraphApiHelper instead.
 */
@singleton()
export class MetaGraphApiCredentialsHandler {
    constructor(
        private readonly _metaGraphApiProvider: MetaGraphApiProvider,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository
    ) {}

    async callApi<T>(params: {
        responseValidator: ZodType<T>;

        credentialId: string;

        /** The ID of the Facebook page or the ID of the Instagram user. */
        fbOrIgPageId: string;

        requestOptions: MetaGraphApiRequestOptions;
    }): Promise<Result<T, MetaGraphApiCredentialsHandlerError>> {
        logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Start', omit(params, 'responseValidator'));

        const credential = await this._facebookCredentialsRepository.getCredentialByIdSafely(params.credentialId);
        if (!credential) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] No credential');
            return err({ code: ApiErrorCodes.CREDENTIAL_NOT_FOUND });
        }

        const pageAccess = credential.pageAccess.find(
            (access) => access.fbPageId === params.fbOrIgPageId || access.igPageId === params.fbOrIgPageId
        );
        const pageAccessToken = pageAccess?.pageAccessToken;
        if (!pageAccess || !pageAccessToken) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] No page access token', {
                pageAccesses: credential.pageAccess,
            });
            return err({ code: ApiErrorCodes.CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND });
        }

        const userAccessToken = credential.userAccessToken;

        logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 1 with current page access token');
        const res1 = await this._metaGraphApiProvider.callApi({
            responseValidator: params.responseValidator,
            token: pageAccessToken,
            requestOptions: params.requestOptions,
        });
        if (res1.isOk()) {
            logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 1 ok');
            await this._updateCredentialLastSeenWorking(params.credentialId, pageAccessToken);
            return res1;
        }
        if (res1.error.code !== MetaGraphApiError.INVALID_TOKEN) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 1 error', { error: res1.error });
            return res1;
        }

        logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 2 after refreshing page access token');
        const refreshRes = await this._refreshPageAccessToken(userAccessToken, pageAccess.fbPageId, params.credentialId);
        if (refreshRes.isErr()) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 2 error refreshing page access token', {
                error: refreshRes.error,
            });
            return err(refreshRes.error);
        }
        const res2 = await this._metaGraphApiProvider.callApi({
            responseValidator: params.responseValidator,
            token: refreshRes.value,
            requestOptions: params.requestOptions,
        });
        if (res2.isOk()) {
            logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 2 ok');
            await this._updateCredentialLastSeenWorking(params.credentialId, refreshRes.value);
            return res2;
        }
        if (res2.error.code !== MetaGraphApiError.INVALID_TOKEN) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 2 error', { error: res2.error });
            return res2;
        }

        logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 3 using user access token');
        const res3 = await this._metaGraphApiProvider.callApi({
            responseValidator: params.responseValidator,
            token: userAccessToken,
            requestOptions: params.requestOptions,
        });
        if (res3.isOk()) {
            logger.info('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 3 ok');
            return res3;
        }
        if (res3.error.code !== MetaGraphApiError.INVALID_TOKEN) {
            logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 3 error', { error: res3.error });
            return res3;
        }

        logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] Try 3 invalid token');
        return res3;
    }

    private async _refreshPageAccessToken(userAccessToken: string, fbPageId: string, credentialId: string) {
        const res = await this._metaGraphApiProvider.callApi({
            responseValidator: refreshPageAccessTokenValidator,
            token: userAccessToken,
            requestOptions: {
                method: 'GET',
                endpoint: fbPageId,
                queryParams: { fields: 'access_token' },
            },
        });
        if (res.isErr()) {
            return res;
        }
        const pageAccessToken = res.value.access_token;
        await this._facebookCredentialsRepository.findOneAndUpdate({
            filter: {
                _id: toDbId(credentialId),
                'pageAccess.fbPageId': fbPageId,
            },
            update: {
                'pageAccess.$.pageAccessToken': pageAccessToken,
            },
        });
        return ok(pageAccessToken);
    }

    private async _updateCredentialLastSeenWorking(credentialId: string, pageAccessToken: string): Promise<void> {
        await this._facebookCredentialsRepository.findOneAndUpdate({
            filter: {
                _id: toDbId(credentialId),
                'pageAccess.pageAccessToken': pageAccessToken,
            },
            update: {
                'pageAccess.$.lastSeenWorking': new Date(),
            },
        });
    }
}
