import * as Sentry from '@sentry/node';
import { compact } from 'lodash';
import assert from 'node:assert/strict';
import { randomUUID } from 'node:crypto';
import { inject, singleton } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { AspectRatio, MalouErrorCode, MediaType, MimeType, PostPublicationStatus, PostType, waitFor } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { UpdateMediaPostIdsService } from ':modules/media/services/update-media-post-ids.service';
import { AwsMediaConvertService } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert.service';
import { UploadImageService } from ':modules/media/use-cases/upload-media-v2/services/upload-image.service';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { SocialPost } from ':modules/posts/v2/entities/social-post.entity';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { SchedulePostPublicationService } from ':modules/posts/v2/services/schedule-post-publication/schedule-post-publication.service';
import * as redis from ':plugins/redis-db';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { SlackService } from ':services/slack.service';

/** This function should only be called on reel posts before publication, duplication etc */
export const waitUntilReelThumbnailIsExtracted = async (postId: string): Promise<void> => {
    logger.info('[PreparePostJob] waiting for thumbnail extraction');
    const begin = new Date();
    // Wait for the thumbnail extraction to finish if it's still running. This won’t
    // loop indefinitely because the key expires after a while.
    while (await redis.client.get(`posts_v2_reel_thumbnail_extraction:${postId}`)) {
        await waitFor(1000);
    }
    const durationMs = new Date().getTime() - begin.getTime();
    logger.info(`[PreparePostJob] thumbnail extracted in ${durationMs} ms`);
};

export const FAKE_TRANSFORM_DATA = {
    width: -1,
    height: -1,
    aspectRatio: AspectRatio.ORIGINAL,
    left: -1,
    top: -1,
    rotationInDegrees: 0,
};

@singleton()
export class UpdateSocialPostUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _schedulePostPublicationService: SchedulePostPublicationService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _updateMediaPostIdsService: UpdateMediaPostIdsService,
        private readonly _slackService: SlackService,
        private readonly _awsMediaConvertService: AwsMediaConvertService,
        private readonly _uploadImageService: UploadImageService,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService
    ) {}

    async execute({ post, author }: { post: SocialPostDto; author: PostAuthorProps }): Promise<SocialPostDto> {
        try {
            logger.info('[POST PUBLICATION] [UPDATE] Started', { post, author });

            const postBeforeUpdate = await this._postsRepository.findOne({ filter: { _id: post.id }, options: { lean: true } });

            if (!postBeforeUpdate) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { postId: post.id } });
            }

            if (postBeforeUpdate.published === PostPublicationStatus.PENDING && postBeforeUpdate.isPublishing) {
                throw new MalouError(MalouErrorCode.POST_IS_PUBLISHING, { metadata: { postId: post.id } });
            }

            if (post.reelThumbnail?.type === 'videoFrame') {
                // We don’t want to use the thumbnail extracted from the video that comes from the client
                // because we will generate our own thumbnails server-side.
                //
                // In theory this is buggy since there’s TOCTOU data race here, but I think it will be
                // basically impossible to fix without a big overhaul of this endpoint.
                if (postBeforeUpdate.thumbnailOffsetTimeInMs && postBeforeUpdate.thumbnail) {
                    // the thumbnail was already extracted from the video so we keep it until
                    // the new one is generated.
                    // Only the field `id` is used below so other fields are set to a fake value.
                    post.reelThumbnail.media = {
                        id: postBeforeUpdate.thumbnail.toString(), // only the field ID is used
                        type: MediaType.PHOTO,
                        aspectRatio: 1,
                        thumbnail1024OutsideUrl: '',
                        thumbnail256OutsideUrl: '',
                        transformData: FAKE_TRANSFORM_DATA,
                    };
                } else {
                    // the post had a custom thumbnail or no thumbnail so we erase it
                    post.reelThumbnail.media = undefined;
                }
            }

            const socialPostUpdate = new SocialPost({
                id: post.id,
                title: post.title ?? undefined,
                text: post.text,
                platformKeys: post.platformKeys,
                published: post.published,
                isPublishing: post.isPublishing,
                postType: post.postType,
                plannedPublicationDate: post.plannedPublicationDate ? new Date(post.plannedPublicationDate) : new Date(),
                hashtags: this._getPostHashtagsFromPostHashtagsDto(post.hashtags),
                attachments: this._getAttachmentsFromPostAttachmentsDto(post.attachments),
                location: this._getPostLocationFromPostLocationDto(post.location),
                callToAction: this._getPostCallToActionFromPostCallToActionDto(post.callToAction),
                feedbacks: this._getPostFeedbacksFromPostFeedbacksDto(post.feedbacks),
                error: post.error ? { code: post.error.code, rawData: post.error.rawData } : undefined,
                socialLink: post.socialLink ?? undefined,
                socialCreatedAt: post.socialCreatedAt ? new Date(post.socialCreatedAt) : undefined,
                userTagsList: post.userTagsList.map((userTags) =>
                    userTags
                        ? userTags.map((userTag) => ({
                              x: userTag.x,
                              y: userTag.y,
                              username: userTag.username,
                          }))
                        : null
                ),
                tiktokOptions: {
                    privacyStatus: post.tiktokOptions.privacyStatus,
                    interactionAbility: {
                        comment: post.tiktokOptions.interactionAbility.comment,
                        duet: post.tiktokOptions.interactionAbility.duet,
                        stitch: post.tiktokOptions.interactionAbility.stitch,
                    },
                    contentDisclosureSettings: {
                        isActivated: post.tiktokOptions.contentDisclosureSettings.isActivated,
                        yourBrand: post.tiktokOptions.contentDisclosureSettings.yourBrand,
                        brandedContent: post.tiktokOptions.contentDisclosureSettings.brandedContent,
                    },
                },
                reelThumbnail: post.reelThumbnail,
                instagramCollaboratorsUsernames: post.instagramCollaboratorsUsernames,
            });

            for (const attachment of socialPostUpdate.attachments ?? []) {
                await this._mediasRepository.updateTransformData(attachment.id, attachment.transformData);
            }

            const postAuthor = new PostAuthor({ id: author.id, name: author.name, lastname: author.lastname });
            const newPost = await this._postsRepository.updateSocialPost(post.id, socialPostUpdate, postAuthor);
            assert(newPost);
            const dto = newPost.toDto();

            if (dto.published === PostPublicationStatus.DRAFT) {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(post.id, { $pull: { postIds: post.id } });
            } else {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(post.id, { $addToSet: { postIds: post.id } });
            }

            if (
                dto.reelThumbnail?.type === 'videoFrame' &&
                dto.reelThumbnail.thumbnailOffsetTimeInMs !== postBeforeUpdate.thumbnailOffsetTimeInMs
            ) {
                // important: this function must be called before triggering the publication
                await this._extractReelThumbnailBackground({
                    postId: newPost.id,
                    thumbnailOffsetTimeInMs: dto.reelThumbnail.thumbnailOffsetTimeInMs,
                });
            }

            if (dto.published === PostPublicationStatus.PENDING) {
                await this._schedulePostPublicationService.schedulePostPublication(
                    author.id,
                    dto.id,
                    socialPostUpdate.plannedPublicationDate
                );
            } else {
                await this._schedulePostPublicationService.cancelPostPublication(dto.id);
            }

            logger.info('[POST PUBLICATION] [UPDATE] Finished', dto);
            return dto;
        } catch (err) {
            logger.error('[POST PUBLICATION] [UPDATE] Error', { post, author });
            await this._slackService.sendAlert({ data: { err } });
            throw err;
        }
    }

    /**
     * Runs _extractReelThumbnail in the background without waiting (it’s not critical and it takes a lot of time)
     */
    private async _extractReelThumbnailBackground(params: { postId: string; thumbnailOffsetTimeInMs: number }) {
        // the publication code will block while this key is set
        const key = `posts_v2_reel_thumbnail_extraction:${params.postId}`;
        await redis.client.set(key, '1', 'EX', 60); // the EXpiration is important
        this._extractReelThumbnail(params)
            .catch((error) => {
                logger.error('[UpdateSocialPostUseCase] _extractReelThumbnail', { ...params, error });
                Sentry.captureException(error);
            })
            .finally(() => redis.client.del(key));
    }

    /**
     * For reels with a thumbnail picture extracted from the video.
     *
     * This function triggers an AWS MediaConvert job so it will typically
     * take ten seconds to complete.
     */
    private async _extractReelThumbnail(params: { postId: string; thumbnailOffsetTimeInMs: number }): Promise<void> {
        logger.info('[UpdateSocialPostUseCase] _extractReelThumbnail', params);
        const dbPost = await this._postsRepository.findOne({ filter: { _id: params.postId }, options: { lean: true } });
        assert(dbPost);
        assert(dbPost.postType === PostType.REEL);
        if (!dbPost.attachments || dbPost.attachments.length === 0) {
            return;
        }

        const dbMedia = await this._mediasRepository.findOne({ filter: { _id: dbPost.attachments[0] }, options: { lean: true } });
        assert(dbMedia);
        assert(dbMedia.type === MediaType.VIDEO);
        assert(dbMedia.duration);

        const uuid = randomUUID();

        let tmpThumbnailKey: string;
        {
            let s3Key: string;
            let width: number;
            let height: number;
            if (dbMedia.isV2) {
                assert(dbMedia.storedObjects);
                assert(dbMedia.dimensions);
                assert(dbMedia.dimensions.normalized);
                s3Key = dbMedia.storedObjects.normalized.key;
                width = dbMedia.dimensions.normalized.width;
                height = dbMedia.dimensions.normalized.height;
            } else {
                assert(dbMedia.dimensions);
                assert(dbMedia.dimensions.original);
                const url = new URL(dbMedia.urls.original);
                s3Key = url.pathname.slice(1);
                width = dbMedia.dimensions.original.width;
                height = dbMedia.dimensions.original.height;
            }
            const result = await this._awsMediaConvertService.generateVideoThumbnail(
                {
                    s3Key,
                    durationInMilliseconds: dbMedia.duration * 1000,
                    widthInPixels: width,
                    heightInPixels: height,
                    startTimestampInMilliseconds: params.thumbnailOffsetTimeInMs,
                },
                {
                    maxDimensionsPx: 1920,
                    s3OutputKeyPrefix: `reel-thumbnail-tmp/${uuid}/thumbnail-1024`,
                }
            );
            if (result.isErr()) {
                throw new Error('generateVideoThumbnail has failed');
            }
            tmpThumbnailKey = result.value.s3key;
        }

        assert(dbPost.restaurantId);
        const uploadResult = await this._uploadImageService.execute({
            source: this._distantStorageService.getReadable(tmpThumbnailKey),
            imageMimeType: MimeType.IMAGE_JPEG,
            uuid,
            userFileName: 'reel-thumbnail',
            restaurantId: dbPost.restaurantId.toString(),
            originalMediaId: dbMedia._id.toString(),
        });
        await this._distantStorageService.delete(tmpThumbnailKey);
        if (!uploadResult.isOk()) {
            throw new Error('UploadImageService has failed');
        }

        const result = await this._postsRepository.updateOne({
            filter: {
                _id: dbPost._id,

                // make sure the thumbnail didn’t change
                thumbnailOffsetTimeInMs: params.thumbnailOffsetTimeInMs,
            },
            update: { $set: { thumbnail: toDbId(uploadResult.value.mediaId) } },
        });
        if (result.modifiedCount === 0) {
            // the thumbnail has been replaced by something else since the job started
            await this._deleteMedia(uploadResult.value.mediaId);
        }
    }

    private async _deleteMedia(mediaId: string) {
        await this._mediasRepository.deleteOne({ filter: { _id: toDbId(mediaId) } });
        await this._distantStorageService.delete(mediaId);
    }

    private _getPostHashtagsFromPostHashtagsDto(postHashtags: SocialPostDto['hashtags']): SocialPost['hashtags'] {
        return postHashtags
            ? {
                  selected: postHashtags.selected.map((hashtag) => ({
                      id: hashtag.id,
                      text: hashtag.text,
                      isCustomerInput: hashtag.isCustomerInput,
                      isMain: !!hashtag.isMain,
                      type: hashtag.type,
                  })),
                  suggested: postHashtags.suggested.map((hashtag) => ({
                      id: hashtag.id,
                      text: hashtag.text,
                      isCustomerInput: hashtag.isCustomerInput,
                      isMain: !!hashtag.isMain,
                      type: hashtag.type,
                  })),
              }
            : undefined;
    }

    private _getAttachmentsFromPostAttachmentsDto(postAttachments: SocialPostDto['attachments']): SocialPost['attachments'] {
        return compact(
            postAttachments.map((attachment) => {
                const base = {
                    id: attachment.id,
                    type: attachment.type,
                    aiDescription: attachment.aiDescription,
                    thumbnail1024OutsideUrl: attachment.thumbnail1024OutsideUrl,
                    thumbnail256OutsideUrl: attachment.thumbnail256OutsideUrl,
                    thumbnail256OutsideDimensions:
                        !!attachment.thumbnail256OutsideDimensions?.width && !!attachment.thumbnail256OutsideDimensions?.height
                            ? {
                                  width: attachment.thumbnail256OutsideDimensions?.width,
                                  height: attachment.thumbnail256OutsideDimensions?.height,
                              }
                            : undefined, // todo strict : remove this ternary
                    thumbnail1024OutsideDimensions:
                        !!attachment.thumbnail1024OutsideDimensions?.width && !!attachment.thumbnail1024OutsideDimensions?.height
                            ? {
                                  width: attachment.thumbnail1024OutsideDimensions?.width,
                                  height: attachment.thumbnail1024OutsideDimensions?.height,
                              }
                            : undefined, // todo strict : remove this ternary
                    transformData: {
                        aspectRatio: attachment.transformData.aspectRatio,
                        rotationInDegrees: attachment.transformData.rotationInDegrees,
                        left: attachment.transformData.left,
                        top: attachment.transformData.top,
                        width: attachment.transformData.width,
                        height: attachment.transformData.height,
                    },
                    aspectRatio: attachment.aspectRatio,
                };
                if (attachment.type === MediaType.PHOTO) {
                    return {
                        ...base,
                        type: MediaType.PHOTO,
                    };
                }
                if (attachment.type === MediaType.VIDEO) {
                    return {
                        ...base,
                        type: MediaType.VIDEO,
                        videoUrl: attachment.videoUrl,
                        videoDimensions: {
                            width: attachment.videoDimensions.width,
                            height: attachment.videoDimensions.height,
                        },
                    };
                }
            })
        );
    }

    private _getPostLocationFromPostLocationDto(postLocation: SocialPostDto['location']): SocialPost['location'] {
        return postLocation
            ? {
                  id: postLocation.id,
                  name: postLocation.name,
                  link: postLocation.link,
                  location:
                      postLocation.location && postLocation.location.latitude && postLocation.location.longitude
                          ? {
                                latitude: postLocation.location.latitude,
                                longitude: postLocation.location.longitude,
                                zip: postLocation.location.zip ?? undefined,
                                city: postLocation.location.city ?? undefined,
                                country: postLocation.location.country ?? undefined,
                                street: postLocation.location.street ?? undefined,
                            }
                          : undefined,
              }
            : undefined;
    }

    private _getPostFeedbacksFromPostFeedbacksDto(postFeedbacks: SocialPostDto['feedbacks']): SocialPost['feedbacks'] {
        return postFeedbacks
            ? {
                  id: postFeedbacks.id,
                  isOpen: postFeedbacks.isOpen,
                  participants: postFeedbacks.participants.map((participant) => ({
                      participant: {
                          id: participant.participant.id,
                          name: participant.participant.name,
                          lastname: participant.participant.lastname ?? undefined,
                          email: participant.participant.email,
                          role: participant.participant.role ?? undefined,
                      },
                      types: participant.types,
                  })),
                  feedbackMessages: postFeedbacks.feedbackMessages.map((message) => ({
                      id: message.id,
                      author: {
                          id: message.author.id,
                          name: message.author.name,
                          lastname: message.author.lastname ?? undefined,
                          profilePictureUrl: message.author.profilePictureUrl ?? undefined,
                          email: message.author.email,
                          role: message.author.role ?? undefined,
                      },
                      createdAt: new Date(message.createdAt),
                      updatedAt: new Date(message.updatedAt),
                      text: message.text,
                      type: message.type,
                      visibility: message.visibility,
                  })),
                  updatedAt: new Date(postFeedbacks.updatedAt),
                  createdAt: new Date(postFeedbacks.createdAt),
              }
            : undefined;
    }

    private _getPostCallToActionFromPostCallToActionDto(postCallToAction: SocialPostDto['callToAction']): SocialPost['callToAction'] {
        if (!postCallToAction) {
            return undefined;
        }

        return {
            actionType: postCallToAction.actionType,
            url: postCallToAction.url,
        };
    }
}
