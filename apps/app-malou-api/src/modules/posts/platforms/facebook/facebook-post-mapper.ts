import assert from 'assert';

import { IPlatform, IPost, toDbId } from '@malou-io/package-models';
import {
    createDate,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    SocialAttachmentsMediaTypes,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { FbAttachmentType } from ':modules/mentions/platforms/facebook/mentions.facebook.mapper';
import { Platform } from ':modules/platforms/platforms.entity';
import { MalouPostData } from ':modules/posts/posts.interface';
import { PostMapper } from ':modules/posts/posts.mapper';

import { FbPostData, FbPostInsight, InsightType } from './facebook-post.interface';

const toMalouPostsTypes = {
    [FbAttachmentType.MULTI_SHARE]: PostType.CAROUSEL,
    [FbAttachmentType.ALBUM]: PostType.CAROUSEL,
    [FbAttachmentType.PHOTO]: PostType.IMAGE,
    [FbAttachmentType.VIDEO]: PostType.VIDEO,
};

type IPostSocialAttachment = IPost['socialAttachments'];

export class FacebookPostMapper extends PostMapper {
    constructor() {
        super();
    }

    mapToMalouPost({ post, platform }: { post: FbPostData; platform: Platform }): MalouPostData {
        if (!platform) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                message: 'Need platform for post',
                metadata: {
                    platform,
                    postId: post.id,
                },
            });
        }

        // todo: this breaks link to previous malou media

        const socialAttachments: IPostSocialAttachment = [];
        if ('attachments' in post) {
            // handle posts with multiple media
            for (const attachment of post.attachments?.data ?? []) {
                if ('subattachments' in attachment && attachment.subattachments?.data && attachment.type !== FbAttachmentType.MULTI_SHARE) {
                    // when multiple media on a post, `subattachments` is present
                    for (const subat of attachment.subattachments.data) {
                        const type: SocialAttachmentsMediaTypes | undefined = subat.type
                            ? {
                                  photo: SocialAttachmentsMediaTypes.IMAGE,
                                  video: SocialAttachmentsMediaTypes.VIDEO,
                              }[subat.type]
                            : undefined;
                        if (!type) {
                            continue;
                        }
                        const socialId = subat.target?.id; // not sure if this is the correct id
                        const url = type === SocialAttachmentsMediaTypes.VIDEO ? subat.media?.source : subat.media?.image?.src;
                        if (!url) {
                            continue;
                        }
                        const media = { type, urls: { original: url }, socialId };
                        socialAttachments.push(media);
                    }
                } else {
                    if (!attachment.media) {
                        continue;
                    }
                    // only one media on a post - this is weird facebook shit
                    const url = attachment.media?.source ?? attachment.media.image?.src;
                    if (!url) {
                        continue;
                    }
                    const type =
                        attachment.media?.source || url.match(/\/\/video/)
                            ? SocialAttachmentsMediaTypes.VIDEO
                            : SocialAttachmentsMediaTypes.IMAGE;
                    let thumbnailUrl: string | undefined;
                    if (type === 'video') {
                        thumbnailUrl = attachment.media.image?.src;
                    }

                    const socialId = null;
                    const media = {
                        type,
                        urls: { original: url },
                        socialId,
                        thumbnailUrl,
                    };
                    socialAttachments.push(media);
                }
            }
        }

        assert(post.created_time, 'Missing created_time on post');
        assert(post.id, 'Missing id on post');
        assert(post.permalink_url, 'Missing permalink_url on post');
        const res: MalouPostData = {
            socialId: post.id,
            socialCreatedAt: new Date(post.created_time),
            sortDate: new Date(post.created_time),
            socialUpdatedAt: post.updated_time ? new Date(post.updated_time) : new Date(post.created_time),
            socialLink: post.permalink_url,
            text: post.message ?? post.story ?? '',
            published: PostPublicationStatus.PUBLISHED,
            key: PlatformKey.FACEBOOK,
            socialAttachments,
            platformId: toDbId(platform._id),
            restaurantId: toDbId(platform.restaurantId),
            source: PostSource.SOCIAL,
            postType:
                socialAttachments.length === 1 && socialAttachments[0].type.valueOf() === FbAttachmentType.VIDEO.valueOf()
                    ? PostType.REEL
                    : (toMalouPostsTypes[socialAttachments[0]?.type ?? FbAttachmentType.PHOTO] ?? PostType.IMAGE),
        };

        return res;
    }

    mapPublishedFbReelToMalouPost(
        fbReel: FacebookApiTypes.Reels.GetReelResponse,
        malouInfo: { restaurantId: string; platformId: string }
    ): MalouPostData {
        const preferredThumbnail = fbReel.thumbnails?.data?.find((t) => t.is_preferred)?.uri;
        const thumbnail = fbReel.thumbnails?.data?.find((t) => !!t.uri)?.uri;
        return {
            socialId: fbReel.id,
            socialCreatedAt: new Date(fbReel.created_time),
            sortDate: new Date(fbReel.created_time),
            socialUpdatedAt: fbReel.updated_time ? new Date(fbReel.updated_time) : new Date(fbReel.created_time),
            title: fbReel.title,
            text: fbReel.description,
            socialLink: `https://facebook.com${fbReel.permalink_url}`,
            socialAttachments: [
                {
                    type: SocialAttachmentsMediaTypes.VIDEO,
                    urls: { original: fbReel.source },
                    thumbnailUrl: preferredThumbnail ?? thumbnail,
                },
            ],

            published: PostPublicationStatus.PUBLISHED,
            key: PlatformKey.FACEBOOK,
            platformId: toDbId(malouInfo.platformId),
            restaurantId: toDbId(malouInfo.restaurantId),
            source: PostSource.SOCIAL,
            postType: PostType.REEL,
        };
    }

    mapToPlatformPost({ post, mediaSocialIds = [] }) {
        const platformPost = {
            message: post.text,
            attached_media: mediaSocialIds.map((id) => ({ media_fbid: id })),
        };

        return platformPost;
    }

    mapToInsightPost(post: FbPostData): FbPostInsight {
        assert(post.id, 'Missing id on post');
        assert(post.created_time, 'Missing created_time on post');
        assert(post.permalink_url, 'Missing permalink_url on post');
        assert(post.username, 'Missing username on post');

        return {
            socialId: post.id,
            username: post.username,
            permalink: post.permalink_url,
            caption: post.message ?? '',
            createdAt: createDate(post.created_time)!,
            shares: post.shares?.count,
            impressions: post.insights?.data?.find((d) => d.name === InsightType.POST_IMPRESSIONS)?.values?.[0]?.value ?? 0,
            likes: post.likes?.summary?.total_count ?? 0,
            comments: post.comments?.summary?.total_count ?? 0,
            postType:
                (post.attachments?.data?.[0]?.media_type ? toMalouPostsTypes[post.attachments.data[0].media_type] : undefined) ||
                PostType.IMAGE,
            url: post.attachments?.data?.[0]?.media?.source || post.attachments?.data?.[0]?.media?.image?.src || '',
            carouselUrls:
                post.attachments?.data?.[0]?.subattachments?.data?.map((subAttachment) => ({
                    url: subAttachment?.media?.image?.src ?? '',
                    type: subAttachment?.media_type ? toMalouPostsTypes[subAttachment.media_type] : PostType.IMAGE,
                })) ?? [],
            socialAttachments: this._mapToMalouSocialAttachments(post) ?? [],
        };
    }

    mapToMalouStory(
        story: FacebookApiTypes.Stories.GetStoryResponse & {
            media: FacebookApiTypes.Stories.GetStoryImageResponse | FacebookApiTypes.Videos.GetVideoSourceResponse;
        },
        platform: IPlatform | Platform
    ): Partial<IPost> {
        return {
            platformId: toDbId(platform._id),
            restaurantId: toDbId(platform.restaurantId),
            socialId: story.post_id,
            socialCreatedAt: new Date(Number(story.creation_time) * TimeInMilliseconds.SECOND),
            socialUpdatedAt: new Date(Number(story.creation_time) * TimeInMilliseconds.SECOND),
            published: PostPublicationStatus.PUBLISHED,
            socialLink: story.url,
            key: PlatformKey.FACEBOOK,
            source: PostSource.SOCIAL,
            socialAttachments: this._mapToMalouStoryMedia(story),
            isStory: true,
            postType: toMalouPostsTypes[story.media_type],
        };
    }

    mapReelToInsightPost(reel: FacebookApiTypes.Reels.GetReelWithInsightsResponse): FbPostInsight {
        const socialActionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isSocialActionsReelInsights);
        const impressionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isImpressionsReelInsights);
        const likesReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isLikesReelInsights);
        const playsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isPlaysReelInsights);
        const preferredThumbnail = reel.thumbnails?.data?.find((t) => t.is_preferred)?.uri;
        const thumbnail = reel.thumbnails?.data?.find((t) => !!t.uri)?.uri;
        return {
            postType: PostType.REEL,
            socialId: reel.id,
            username: '',
            permalink: reel.permalink_url,
            caption: reel.description,
            createdAt: new Date(reel.created_time),
            shares: socialActionsReelInsights?.values[0]?.value?.SHARE ?? 0,
            impressions: impressionsReelInsights?.values[0].value ?? 0,
            likes: likesReelInsights?.values[0]?.value?.REACTION_LIKE ?? 0,
            comments: socialActionsReelInsights?.values[0]?.value?.COMMENT ?? 0,
            plays: playsReelInsights?.values[0].value,
            url: reel.source,
            carouselUrls: [],
            socialAttachments: [
                { type: SocialAttachmentsMediaTypes.VIDEO, urls: { original: reel.source }, thumbnailUrl: preferredThumbnail ?? thumbnail },
            ],
            thumbnail: reel.picture,
        };
    }

    private _mapToMalouSocialAttachments(post: FbPostData): IPostSocialAttachment {
        const postAttachmentDatum = post.attachments?.data?.[0];
        const postType = this._mapToMalouPostType(postAttachmentDatum?.media_type as FbAttachmentType);

        let originalUrl: string | undefined;

        switch (postType) {
            case PostType.IMAGE:
                originalUrl = postAttachmentDatum?.media?.source ?? postAttachmentDatum?.media?.image?.src;
                assert(originalUrl);
                return [
                    {
                        type: SocialAttachmentsMediaTypes.IMAGE,
                        urls: { original: originalUrl },
                    },
                ];
            case PostType.VIDEO:
                originalUrl = postAttachmentDatum?.media?.source;
                assert(originalUrl);
                return [
                    {
                        type: SocialAttachmentsMediaTypes.VIDEO,
                        urls: { original: originalUrl },
                    },
                ];
            case PostType.CAROUSEL:
                return (
                    postAttachmentDatum?.subattachments?.data?.map((subattachment) => ({
                        type:
                            subattachment.media_type === FbAttachmentType.PHOTO
                                ? SocialAttachmentsMediaTypes.IMAGE
                                : SocialAttachmentsMediaTypes.VIDEO,
                        urls: { original: subattachment.media?.source ?? subattachment.media?.image?.src ?? '' },
                    })) ?? []
                );
            default:
                throw new MalouError(MalouErrorCode.INVALID_DATA, {
                    metadata: {
                        post,
                        platform: PlatformKey.FACEBOOK,
                    },
                });
        }
    }

    private _mapToMalouPostType(fbAttachmentType: FbAttachmentType): PostType {
        return toMalouPostsTypes[fbAttachmentType] ?? PostType.IMAGE;
    }

    private _mapToMalouStoryMedia(story: FacebookApiTypes.Stories.GetStoryResponse): IPostSocialAttachment {
        const storyMedia: FacebookApiTypes.Videos.GetVideoSourceResponse | FacebookApiTypes.Stories.GetStoryImageResponse | undefined =
            story.media;
        const postType = this._mapToMalouPostType(this._mapStoryMediaTypeToFbAttachmentType(story?.media_type));

        switch (postType) {
            case PostType.IMAGE:
                return [
                    {
                        type: SocialAttachmentsMediaTypes.IMAGE,
                        urls: { original: (storyMedia as FacebookApiTypes.Stories.GetStoryImageResponse)?.images?.[0]?.source ?? '' },
                    },
                ];
            case PostType.VIDEO:
                return [
                    {
                        type: SocialAttachmentsMediaTypes.VIDEO,
                        urls: { original: (storyMedia as FacebookApiTypes.Videos.GetVideoSourceResponse)?.source ?? '' },
                    },
                ];
            default:
                throw new MalouError(MalouErrorCode.INVALID_DATA, {
                    metadata: {
                        story,
                        platform: PlatformKey.FACEBOOK,
                    },
                });
        }
    }

    private _mapStoryMediaTypeToFbAttachmentType(mediaType: FacebookApiTypes.Stories.StoryMediaType): FbAttachmentType {
        if (mediaType === FacebookApiTypes.Stories.StoryMediaType.VIDEO) {
            return FbAttachmentType.VIDEO;
        }
        return FbAttachmentType.PHOTO;
    }
}
