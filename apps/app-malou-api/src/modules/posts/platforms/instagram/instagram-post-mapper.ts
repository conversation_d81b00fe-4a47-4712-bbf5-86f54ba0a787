import assert from 'assert';

import { IPost, ISocialAttachment, toDbId } from '@malou-io/package-models';
import {
    createDate,
    FacebookApiMediaType,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    roundToDecimals,
    SocialAttachmentsMediaTypes,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { toMalouPostsTypes } from ':modules/mentions/platforms/instagram/mentions.instagram.mapper';
import { Platform } from ':modules/platforms/platforms.entity';
import { MalouPostData } from ':modules/posts/posts.interface';
import { PostMapper } from ':modules/posts/posts.mapper';

import {
    IgMediaProductType,
    IgPostData,
    IgPostInsightFbName,
    IgStoryData,
    PostInsight,
    PostInsightWithStats,
} from './instagram-post.interface';

export class InstagramPostMapper extends PostMapper {
    constructor() {
        super();
    }

    mapToMalouPost({ post, platform }: { post: IgPostData; platform: Platform }): MalouPostData {
        if (!platform) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                metadata: {
                    post,
                    platform: PlatformKey.INSTAGRAM,
                },
            });
        }

        assert(post.id, 'Missing id on post');
        assert(post.timestamp, 'Missing timestamp on post');
        assert(post.permalink, 'Missing permalink on post');

        const res: MalouPostData = {
            socialId: post.id,
            socialCreatedAt: new Date(post.timestamp),
            sortDate: new Date(post.timestamp),
            socialUpdatedAt: new Date(post.timestamp),
            socialLink: post.permalink,
            text: post.caption ?? '',
            published: PostPublicationStatus.PUBLISHED,
            key: PlatformKey.INSTAGRAM,
            socialAttachments: [],
            source: PostSource.SOCIAL,
            postType: post.media_product_type === IgMediaProductType.REELS ? PostType.REEL : toMalouPostsTypes[post.media_type],
            isReelDisplayedInFeed:
                post.media_product_type === IgMediaProductType.FEED ||
                ((post.media_product_type === IgMediaProductType.REELS && post.is_shared_to_feed) ?? true),
            restaurantId: toDbId(platform.restaurantId),
            platformId: toDbId(platform._id),
        };

        switch (post.media_type) {
            case FacebookApiMediaType.IMAGE:
                assert(post.media_url, 'Missing media_url on post');
                res.socialAttachments!.push({
                    type: SocialAttachmentsMediaTypes.IMAGE,
                    urls: { original: post.media_url },
                });
                break;
            case FacebookApiMediaType.VIDEO:
                // META bug : reels with copyrighted audio don't have media_url
                // https://developers.facebook.com/support/bugs/404495981650858/
                res.socialAttachments!.push({
                    type: post.media_url ? SocialAttachmentsMediaTypes.VIDEO : SocialAttachmentsMediaTypes.IMAGE,
                    urls: { original: post.media_url ?? post.thumbnail_url },
                    thumbnailUrl: post.thumbnail_url,
                });
                break;
            case FacebookApiMediaType.CAROUSEL_ALBUM:
                for (const attachment of post.children?.data ?? []) {
                    res.socialAttachments!.push({
                        thumbnailUrl: attachment.thumbnail_url,
                        type:
                            attachment.media_type === FacebookApiMediaType.VIDEO
                                ? SocialAttachmentsMediaTypes.VIDEO
                                : SocialAttachmentsMediaTypes.IMAGE,
                        urls: { original: attachment.media_url || 'https://via.placeholder.com/300' }, // sometimes instagram does not send the url so we set up a backup image
                    });
                }
                res.socialAttachments?.sort((_a, b) => {
                    if (b.urls.original?.includes('via.placeholder')) {
                        return -1;
                    }
                    return 1;
                }); // put media where url exists first
                break;
            default:
                throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                    metadata: {
                        post,
                        platform: PlatformKey.INSTAGRAM,
                    },
                });
        }

        return res;
    }

    mapToPlatformPost({ post, mediaSocialIds = [] }) {
        const platformPost = {
            message: post.text,
            attached_media: mediaSocialIds.map((id) => ({ media_fbid: id })),
        };

        return platformPost;
    }

    mapToInsightPost(post: IgPostData): PostInsight {
        assert(post.id, 'Missing id on post');
        assert(post.timestamp, 'Missing timestamp on post');
        assert(post.permalink, 'Missing permalink on post');
        assert(post.media_url, 'Missing media_url on post');

        return {
            postType: post.media_product_type === IgMediaProductType.REELS ? PostType.REEL : toMalouPostsTypes[post.media_type],
            socialId: post.id,
            username: post.username ? `@${post.username}` : '', // hack: no field username if username is undefined
            permalink: post.permalink,
            caption: post.caption?.replace(/(\n)+/g, '') ?? '',
            createdAt: createDate(post.timestamp) ?? new Date(),
            impressions:
                post.insights?.data?.find((d) => d.name === IgPostInsightFbName.VIEWS)?.values[0]?.value || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                post.insights?.data?.find((d) => d.name === IgPostInsightFbName.IMPRESSIONS)?.values[0]?.value ||
                0,
            reach: post.insights?.data?.find((d) => d.name === IgPostInsightFbName.REACH)?.values[0]?.value || 0,
            plays:
                post.insights?.data?.find((d) => d.name === IgPostInsightFbName.VIEWS)?.values[0]?.value || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                post.insights?.data?.find((d) => d.name === IgPostInsightFbName.PLAYS)?.values[0]?.value ||
                post.insights?.data?.find((d) => d.name === IgPostInsightFbName.VIDEO_VIEWS)?.values[0]?.value ||
                0,
            likes: post.insights?.data?.find((d) => d.name === IgPostInsightFbName.LIKES)
                ? (post.insights.data?.find((d) => d.name === IgPostInsightFbName.LIKES)?.values[0]?.value ?? 0)
                : (post?.like_count ?? 0),
            comments: post.insights?.data?.find((d) => d.name === IgPostInsightFbName.COMMENTS)
                ? (post.insights.data?.find((d) => d.name === IgPostInsightFbName.COMMENTS)?.values[0]?.value ?? 0)
                : (post.comments_count ?? 0),
            saved: post.insights?.data?.find((d) => d.name === IgPostInsightFbName.SAVED)?.values[0]?.value || 0,
            shares: post.insights?.data?.find((d) => d.name === IgPostInsightFbName.SHARES)?.values[0]?.value || 0,
            url: post.media_url,
            thumbnail: post.media_type === FacebookApiMediaType.VIDEO ? post.thumbnail_url : undefined,
            thumbnailUrl: post.media_type === FacebookApiMediaType.VIDEO ? post.thumbnail_url : post.media_url,
            carouselUrls:
                post.media_type === FacebookApiMediaType.CAROUSEL_ALBUM
                    ? (post.children?.data?.map((child) => ({
                          url: child?.media_url,
                          type: toMalouPostsTypes[child?.media_type],
                      })) ?? [])
                    : [],
        };
    }

    mapToPostInsightsStatistics(postInsight: PostInsight, followersOnCreationTimeCount?: number): PostInsightWithStats {
        const impressions = (postInsight.postType === PostType.REEL ? postInsight.plays : postInsight.impressions) ?? 0;
        const interactions = (postInsight.comments ?? 0) + (postInsight.likes ?? 0) + (postInsight.saved ?? 0) + (postInsight.shares ?? 0);
        const engagementRate =
            followersOnCreationTimeCount === null || followersOnCreationTimeCount === undefined || followersOnCreationTimeCount === 0
                ? undefined
                : roundToDecimals((interactions / followersOnCreationTimeCount) * 100, 2);

        return {
            ...postInsight,
            stats: {
                impressions,
                interactions,
                engagementRate,
            },
        };
    }

    mapToMalouStory(post: IgStoryData, platform: Platform): Partial<IPost> {
        return {
            platformId: toDbId(platform._id),
            restaurantId: toDbId(platform.restaurantId),
            socialId: post.id,
            socialCreatedAt: new Date(post.timestamp),
            socialUpdatedAt: new Date(post.timestamp),
            published: PostPublicationStatus.PUBLISHED,
            socialLink: post.permalink,
            key: PlatformKey.INSTAGRAM,
            source: PostSource.SOCIAL,
            socialAttachments: this._mapToMalouSocialAttachments(post),
            isStory: true,
            postType: toMalouPostsTypes[post.media_type],
        };
    }

    private _mapToMalouSocialAttachments(post: IgStoryData): ISocialAttachment[] {
        switch (post.media_type) {
            case FacebookApiMediaType.IMAGE:
                return [
                    {
                        type: SocialAttachmentsMediaTypes.IMAGE,
                        urls: { original: post.media_url },
                    },
                ];
            case FacebookApiMediaType.VIDEO:
                return [
                    {
                        type: SocialAttachmentsMediaTypes.VIDEO,
                        urls: { original: post.media_url },
                    },
                ];
            default:
                throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                    metadata: { post },
                });
        }
    }
}
