import { isArray, omit } from 'lodash';
import { ClientSession, FilterQuery, ProjectionFields, UpdateQuery, UpdateWithAggregationPipeline, UpdateWriteOpResult } from 'mongoose';
import { singleton } from 'tsyringe';

import {
    AppPopulateOption,
    EntityRepository,
    FindOneResult,
    ID,
    IPopulatedPost,
    IPost,
    PostModel,
    ReadPreferenceMode,
    toDbId,
    toDbIds,
    VirtualsGetter,
} from '@malou-io/package-models';
import { isNotNil, PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { BasicFilters } from ':helpers/filters/basic-filters';
import { PostsFilters } from ':helpers/filters/posts-filter';
import { Pagination } from ':helpers/pagination';

const SORT_DATE_COMPUTATION = () => ({
    sortDate: { $ifNull: ['$socialCreatedAt', { $ifNull: ['$plannedPublicationDate', new Date()] }] },
});
const SET_SORT_DATE = () => ({ $set: SORT_DATE_COMPUTATION() });

@singleton()
export default class PostsRepository extends EntityRepository<IPost> {
    constructor() {
        super(PostModel);
    }

    async findOneAndUpdate<
        Projection extends ProjectionFields<IPost> | (keyof IPost)[],
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<IPost>>;
            new?: boolean;
            session?: ClientSession;
        } = {},
    >({
        filter,
        update,
        projection,
        options,
    }: {
        filter: FilterQuery<IPost>;
        update: UpdateQuery<IPost> | UpdateWithAggregationPipeline | undefined;
        projection?: Projection | null;
        options?: Options;
    }): Promise<FindOneResult<IPost, VirtualsGetter<IPost>, Projection, Options> | null> {
        return await super.findOneAndUpdate({
            filter,
            update: update ? this._getEnrichedUpdate(update) : {},
            ...(projection && { projection }),
            ...(options && { options }),
        });
    }

    async updateOne({
        filter,
        update,
    }: {
        filter: FilterQuery<IPost>;
        update: UpdateQuery<IPost> | Partial<IPost> | UpdateWithAggregationPipeline;
    }): Promise<UpdateWriteOpResult> {
        return await super.updateOne({
            filter,
            update: this._getEnrichedUpdate(update),
        });
    }

    private _getEnrichedUpdate(update: UpdateQuery<IPost> | UpdateWithAggregationPipeline | Partial<IPost>): UpdateWithAggregationPipeline {
        const setSortDateStage = SET_SORT_DATE();

        let updateWithSortDate: UpdateWithAggregationPipeline = [];
        // Case UpdateWithAggregationPipeline, just add the sort date computation in a $set stage
        if (isArray(update)) {
            updateWithSortDate = [...update, setSortDateStage];
        } else {
            // Case Partial<IPost>, add the sort date computation in a $set stage after wrapping the partial post in a $set stage
            if (this._updateIsPartialPost(update)) {
                updateWithSortDate = [{ $set: update }, setSortDateStage];
            } else {
                // Case UpdateQuery<IPost>, add the sort date computation to the $set stage of the query
                const { $set, ...rest } = update;
                updateWithSortDate = [{ ...rest, $set: { ...$set, ...SORT_DATE_COMPUTATION() } }];
            }
        }

        return updateWithSortDate;
    }

    findById(postId: string): Promise<IPost | null> {
        return this.findOne({ filter: { _id: toDbId(postId) }, options: { lean: true } });
    }

    /**
     * Upsert a post on its restaurantId and socialId
     */
    async upsertPostByRestaurantIdAndSocialId(post: Partial<IPost>): Promise<IPost> {
        // Delete id due to upsert behavior
        delete post._id;
        const { restaurantId, socialId } = post;
        return this.upsert({ filter: { restaurantId, socialId }, update: post });
    }

    async getDistinctRestaurantIdsFromPostIds(postIds: string[]): Promise<string[]> {
        const restaurantIds = await this.model.distinct('restaurantId', { _id: { $in: toDbIds(postIds) } });
        return restaurantIds.filter(isNotNil).map((id) => id.toString());
    }

    async getDistinctPublishedFromPostIds(postIds: string[]): Promise<string[]> {
        const publishedList = await this.model.distinct('published', { _id: { $in: toDbIds(postIds) } });
        return publishedList;
    }

    /**
     * Get paginated, filtered posts with populated authors
     */
    getRestaurantPostsPaginated = ({ pagination, filters }: { pagination: Pagination; filters: PostsFilters }) => {
        const sortOrder = parseInt(filters.sortOrder, 10);
        const searchQuery = filters.buildQuery();

        const feedbacksLookupPipeline = [
            {
                $lookup: {
                    from: 'feedbacks',
                    localField: 'feedbackId',
                    foreignField: '_id',
                    as: 'feedback',
                },
            },
            {
                $unwind: {
                    path: '$feedback',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];

        // This pipeline lookup attachments and preserve order
        // The input documents need to be Post
        // The output document is a Post with attachments (ID array) field replaced by attachments (object looked up)
        const attachmentsOrderedLookupPipeline = [
            // Simple lookup does not preserve attachments order
            // So we need to use the second method from this answer:
            // https://stackoverflow.com/questions/55033804/aggregate-lookup-does-not-return-elements-original-array-order
            // (The first one is too slow)
            {
                $lookup: {
                    from: 'media',
                    localField: 'attachments',
                    foreignField: '_id',
                    as: 'attachmentsLookup',
                },
            },
            {
                $unwind: {
                    path: '$attachmentsLookup',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: 'media',
                    localField: 'thumbnail',
                    foreignField: '_id',
                    as: 'thumbnail',
                },
            },
            {
                $unwind: {
                    path: '$thumbnail',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    attachmentIndex: {
                        $indexOfArray: ['$attachments', '$attachmentsLookup._id'],
                    },
                },
            },
            { $sort: { _id: 1, attachmentIndex: 1 } },
            // At this point, we got the field $attachmentsLookup with the attachments looked up in the right order
            // But it is unwind-ed, so we need to group it by id
            {
                $group: {
                    _id: '$_id',
                    attachments: { $push: '$attachmentsLookup' },
                    document: { $first: '$$ROOT' },
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: ['$document', { attachments: '$attachments' }],
                    },
                },
            },
            {
                $project: {
                    attachmentsLookup: 0,
                    attachmentIndex: 0,
                },
            },
        ];

        const pipeline: any[] = [
            { $match: searchQuery },
            {
                $addFields: {
                    creationDate: { $ifNull: ['$socialCreatedAt', { $ifNull: ['$plannedPublicationDate', '$updatedAt'] }] },
                },
            },
            {
                $match: {
                    creationDate: {
                        $ne: null,
                        $gte: filters.startDate,
                        $lte: filters.endDate,
                    },
                },
            },
            // The sort on _id is important because creationDate can be the same for multiple posts
            // So sorting on _id (above creationDate) assure consistency
            { $sort: { creationDate: sortOrder, _id: -1 } },
            { $skip: pagination.skip ?? pagination.pageNumber * pagination.pageSize },
            { $limit: pagination.pageSize },
            ...feedbacksLookupPipeline,
            ...attachmentsOrderedLookupPipeline,
            { $sort: { creationDate: sortOrder } },
        ];

        return this.aggregate(pipeline);
    };

    getRestaurantPostsCount = async ({ filters }: { filters: PostsFilters }): Promise<number> => {
        const searchQuery = filters.buildQuery();
        const pipeline: any[] = [
            { $match: searchQuery },
            {
                $addFields: {
                    creationDate: { $ifNull: ['$socialCreatedAt', { $ifNull: ['$plannedPublicationDate', '$updatedAt'] }] },
                },
            },
            {
                $match: {
                    creationDate: {
                        $ne: null,
                        $gte: filters.startDate,
                        $lte: filters.endDate,
                    },
                },
            },
            {
                $count: 'count',
            },
        ];
        const result = await this.aggregate(pipeline);
        return result[0]?.count ?? 0;
    };

    countPublishedForRestaurantPlatformAndPeriod({
        restaurantId,
        platformKey,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        platformKey: PlatformKey;
        startDate: Date;
        endDate: Date;
    }): Promise<number> {
        return this.countDocuments({
            filter: {
                restaurantId,
                key: platformKey,
                published: PostPublicationStatus.PUBLISHED,
                socialCreatedAt: { $gte: startDate, $lte: endDate },
                isStory: { $ne: true },
            },
        });
    }

    /**
     * Upsert a post on its text and socialCreatedAt
     */
    upsertPostByTextAndSocialCreatedAt = async (doc: IPost) => {
        return this.upsert({
            filter: {
                restaurantId: doc.restaurantId,
                key: doc.key,
                text: doc.text,
                socialCreatedAt: doc.socialCreatedAt,
            },
            update: omit(doc, '_id'), // delete id due to upsert behavior
        });
    };

    /**
     * Set published to false to all posts except those with ids `ninPostSocialIds`
     */
    setRestaurantUnpublishedPosts = async ({ platformId, ninPostSocialIds }): Promise<any> => {
        const setSortDateStage = SET_SORT_DATE();

        return this.updateMany({
            filter: { socialId: { $nin: ninPostSocialIds }, platformId, published: PostPublicationStatus.PUBLISHED },
            update: [
                {
                    $set: { published: PostPublicationStatus.PENDING },
                },
                {
                    $unset: ['socialLink', 'socialCreatedAt', 'socialUpdatedAt', 'plannedPublicationDate'],
                },
                setSortDateStage,
            ],
        });
    };

    async getScoreListOfPostedPostsByRestaurantId(
        restaurantId: string,
        startDate: Date,
        endDate: Date,
        previousPeriod: boolean
    ): Promise<number[]> {
        const basicFilter = new BasicFilters({
            restaurantId,
            startDate,
            endDate,
            previousPeriod,
        });
        const filter =
            basicFilter.startDate && basicFilter.endDate
                ? { socialCreatedAt: { $gte: basicFilter.startDate, $lt: basicFilter.endDate } }
                : {};
        return this._getFilteredPostsByRestaurantId(restaurantId, filter);
    }

    async getScoreListOfPostedPostsByRestaurantIdAndPlatformKey(
        restaurantId: string,
        startDate: Date,
        endDate: Date,
        previousPeriod: boolean,
        platformKey: PlatformKey
    ): Promise<number[]> {
        const basicFilter = new BasicFilters({
            restaurantId,
            startDate,
            endDate,
            previousPeriod,
        });
        const filter =
            basicFilter.startDate && basicFilter.endDate
                ? { socialCreatedAt: { $gte: basicFilter.startDate, $lt: basicFilter.endDate } }
                : {};

        return this._getFilteredPostsByRestaurantId(restaurantId, { ...filter, key: platformKey });
    }

    /*
    TODO: Split in two methods upsert or update depending on the draft status
    */
    async updatePostAndReturnUpdatedPopulatedPost(postId: string, updateData: Partial<IPost>): Promise<IPopulatedPost | null> {
        const cleanUpdateData = {
            ...updateData,
            ...(updateData.attachments && {
                attachments: updateData.attachments?.map((attachment: any) =>
                    typeof attachment === 'string' ? toDbId(attachment) : toDbId(attachment.id)
                ),
            }),
            ...(updateData.thumbnail && {
                thumbnail:
                    typeof updateData.thumbnail === 'string'
                        ? toDbId(updateData.thumbnail)
                        : toDbId(updateData.thumbnail?.id?.toString() ?? (updateData.thumbnail as any)?.id),
            }),
            ...(updateData.feedbackId && { feedbackId: toDbId(updateData.feedbackId) }),
        };
        if (updateData.restaurantId) {
            cleanUpdateData.restaurantId = toDbId(updateData.restaurantId);
        }

        const setSortDateStage = SET_SORT_DATE();

        return super.findOneAndUpdate({
            filter: { _id: postId },
            update: [
                {
                    $set: cleanUpdateData,
                },
                setSortDateStage,
            ],
            options: {
                populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }],
                lean: true,
            },
        }) as Promise<IPopulatedPost | null>;
    }

    async getPublishedPostsByRestaurantIdAndSource(restaurantId: ID, dateFilter: { $gte: Date; $lte: Date }, source: PostSource) {
        return this.find({
            filter: {
                restaurantId,
                $or: [
                    { socialCreatedAt: dateFilter, createdAt: dateFilter },
                    { plannedPublicationDate: dateFilter, author: { $ne: null } },
                ],
                published: PostPublicationStatus.PUBLISHED,
                source,
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
    }

    async get2LatestPublishedPostsByRestaurantIdAndSource({ restaurantId, source }: { restaurantId: string; source: PostSource }) {
        return this.find({
            filter: {
                restaurantId,
                published: PostPublicationStatus.PUBLISHED,
                source,
                text: { $ne: null },
            },
            projection: { text: 1, language: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED, limit: 2, sort: { socialCreatedAt: -1 } },
        });
    }

    private async _getFilteredPostsByRestaurantId(
        restaurantId: string,
        filter: {
            socialCreatedAt?: { $gte: Date; $lt: Date };
            key?: PlatformKey;
        }
    ): Promise<number[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        published: PostPublicationStatus.PUBLISHED,
                        ...filter,
                    },
                },
                {
                    $group: {
                        _id: null,
                        scoreList: {
                            // $push remove missing fields (surely because undefined value does not exists in BSON)
                            $push: '$keywordAnalysis.score',
                        },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            }
        ).then((res) => res?.[0]?.scoreList || []);
    }

    private _updateIsPartialPost(data: UpdateQuery<IPost> | Partial<IPost>): data is Partial<IPost> {
        return Object.keys(data).every((key) => !key.startsWith('$'));
    }
}
