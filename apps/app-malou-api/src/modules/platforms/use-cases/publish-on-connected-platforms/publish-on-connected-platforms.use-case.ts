import { singleton } from 'tsyringe';

import { I<PERSON><PERSON>urant, PopulateBuilderHelper } from '@malou-io/package-models';
import { HeapEventName, InformationUpdateProvider, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { PublishOnAppleBusinessConnectPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-apple-business-connect-platform.use-case';
import { PublishOnFacebookPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-facebook-platform.use-case';
import { PublishOnFoursquarePlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-foursquare-platform.use-case';
import { PublishOnGmbPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-gmb-platform.use-case';
import { PublishOnUberEatsPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-ubereats-platform.use-case';
import { generateCloudWatchUrl } from ':plugins/aws';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { SlackService } from ':services/slack.service';

export type RestaurantPopulatedToPublish = PopulateBuilderHelper<
    IRestaurant,
    [
        { path: 'category' },
        { path: 'logo' },
        { path: 'cover' },
        { path: 'categoryList' },
        { path: 'attributeList'; populate: [{ path: 'attribute' }] },
    ]
>;

export interface PublishOnPlatformResponse {
    success: boolean;
    statusCode?: number;
    message?: string;
    error?: string;
    malouErrorCode?: MalouErrorCode;
    errors?: { field: string; reason: string }[];
}

@singleton()
export class PublishOnConnectedPlatformsUseCase {
    constructor(
        private readonly _publishOnAppleBusinessConnectPlatformUseCase: PublishOnAppleBusinessConnectPlatformUseCase,
        private readonly _publishOnFacebookPlatformUseCase: PublishOnFacebookPlatformUseCase,
        private readonly _publishOnFoursquarePlatformUseCase: PublishOnFoursquarePlatformUseCase,
        private readonly _publishOnGmbPlatformUseCase: PublishOnGmbPlatformUseCase,
        private readonly _publishOnUberEatsPlatformUseCase: PublishOnUberEatsPlatformUseCase,
        private readonly _slackService: SlackService,
        private readonly _heapService: HeapAnalyticsService
    ) {}

    async execute({
        platformKey,
        restaurant,
        keysToUpdate,
    }: {
        platformKey: PlatformKey;
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate: (keyof RestaurantPopulatedToPublish)[];
    }): Promise<PublishOnPlatformResponse> {
        const restaurantId = restaurant._id.toString();

        try {
            const publishOnPlatformUseCase = {
                [PlatformKey.ABC]: this._publishOnAppleBusinessConnectPlatformUseCase,
                [PlatformKey.FACEBOOK]: this._publishOnFacebookPlatformUseCase,
                [PlatformKey.FOURSQUARE]: this._publishOnFoursquarePlatformUseCase,
                [PlatformKey.GMB]: this._publishOnGmbPlatformUseCase,
                [PlatformKey.UBEREATS]: this._publishOnUberEatsPlatformUseCase,
            }[platformKey];

            if (!publishOnPlatformUseCase) {
                throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_NOT_IMPLEMENTED, {
                    message: `${platformKey} not implemented in PublishOnConnectedPlatformUseCase`,
                });
            }

            logger.info(`[PUBLISH INFOS] About to update data`, {
                restaurantId,
                platformKey,
            });
            this._heapService.track({
                eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_START,
                identity: restaurantId,
                properties: {
                    restaurantId,
                    provider: InformationUpdateProvider.MALOU,
                    platformKey,
                },
            });

            const response: PublishOnPlatformResponse = await publishOnPlatformUseCase.execute({
                restaurant,
                keysToUpdate,
            });

            if (response.success) {
                logger.info(`[PUBLISH INFOS] Data updated`, {
                    restaurantId,
                    platformKey,
                    response,
                });

                this._heapService.track({
                    eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_END,
                    identity: restaurantId,
                    properties: {
                        status: 'success',
                        restaurantId,
                        provider: InformationUpdateProvider.MALOU,
                        platformKey,
                    },
                });
            } else {
                logger.error(`[PUBLISH INFOS] Error while updating data`, {
                    restaurantId,
                    platformKey,
                    response,
                });

                this._heapService.track({
                    eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_END,
                    identity: restaurantId,
                    properties: {
                        status: 'fail',
                        restaurantId,
                        provider: InformationUpdateProvider.MALOU,
                        platformKey,
                    },
                });

                // Send slack alert
                await this._slackService.sendMessage({
                    text: await this._formatSlackMessage({ response, platformKey, restaurantId }),
                });
            }

            return response;
        } catch (error: any) {
            const response = {
                success: false,
                statusCode: 500,
                error: error?.message || error,
            };

            if (error instanceof MalouError && error.malouErrorCode === MalouErrorCode.PLATFORM_SERVICE_NOT_IMPLEMENTED) {
                logger.warn('[PUBLISH INFOS] Update on this platform is not available', {
                    restaurantId,
                    platformKey,
                });

                return response;
            }

            logger.error('[PUBLISH INFOS] Update on this platform failed', {
                restaurantId,
                platformKey,
                error,
            });

            this._heapService.track({
                eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_END,
                identity: restaurantId,
                properties: {
                    status: 'fail',
                    restaurantId,
                    provider: InformationUpdateProvider.MALOU,
                    platformKey,
                },
            });

            // Send Slack alert
            await this._slackService.sendMessage({
                text: await this._formatSlackMessage({ response, platformKey, restaurantId }),
            });

            return response;
        }
    }

    private async _formatSlackMessage({
        response,
        platformKey,
        restaurantId,
    }: {
        response: PublishOnPlatformResponse;
        platformKey: PlatformKey;
        restaurantId: string;
    }): Promise<string> {
        const context = await this._slackService.createContextForSlack({ restaurantId });
        const restaurantInfo = `\nOn ${platformKey}`;
        const updateErrors = response.errors
            ?.filter((error) => error?.reason)
            ?.map(
                (error) =>
                    // eslint-disable-next-line max-len
                    `\n - *field:* \`${error.field}\`, *reason:* \`${error.reason}\`, <${generateCloudWatchUrl({ messageFilter: error.reason })}|Logs>`
            )
            ?.join('');
        const updateError = response.error ? `\n - *error:* ${response.error}` : '';

        return `*Error while updating restaurant*${restaurantInfo}${updateErrors}${updateError}${context}`;
    }
}
