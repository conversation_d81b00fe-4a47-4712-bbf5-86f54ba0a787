import { container } from 'tsyringe';

import { ID, toDbId } from '@malou-io/package-models';
import { InvalidPlatformReason, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCategory } from ':modules/categories/tests/categories.builder';
import { FacebookCategoryIdEnum, GmbCategoryIdEnum } from ':modules/categories/types';
import { getDefaultCredential } from ':modules/credentials/tests/credential.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { PublishOnFacebookPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-facebook-platform.use-case';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { SlackService } from ':services/slack.service';

const MockMalouError = MalouError as jest.MockedClass<typeof MalouError>;
const MockMalouErrorCode = MalouErrorCode as jest.Mocked<typeof MalouErrorCode>;
jest.mock(':modules/credentials/platforms/facebook/facebook.use-cases', () => ({
    ...jest.requireActual(':modules/credentials/platforms/facebook/facebook.use-cases'), // Keeps the original implementation
    postFields: jest.fn((_credentialId: ID, _pageId: string, dataToPublish: { [key: string]: any }) => {
        const fieldToUpdate = Object.keys(dataToPublish)[0];
        switch (fieldToUpdate) {
            case 'is_owned':
            case 'category':
            case 'about':
            case 'overall_star_rating':
                return new Promise((resolve, reject) => {
                    const error = new MockMalouError(MockMalouErrorCode.PLATFORM_MAPPER_FAILED_FIELD_PUBLISH);
                    reject(error);
                });
            default:
                return new Promise((resolve, _reject) => resolve({ success: true }));
        }
    }),
    isBrandPage: jest.fn((_credentialId: ID, _pageId: string) => false),
}));

describe('facebookPlatformUseCases', () => {
    beforeEach(() => {
        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });
    describe('publishFieldsUseCases', () => {
        afterAll(() => {
            jest.resetAllMocks();
        });

        it('should update succeed', async () => {
            const publishOnFacebookUseCase = container.resolve(PublishOnFacebookPlatformUseCase);
            const updateResults = await publishOnFacebookUseCase.publishFields({
                credentialId: 'azerty',
                pageId: '12345',
                dataToPublish: {
                    name: 'les copains suisses',
                    website: 'www.malou.com',
                },
                restaurantId: '123',
            });
            expect(updateResults).toEqual({ errors: [] });
        });

        it('should update fail for all fields', async () => {
            const publishOnFacebookUseCase = container.resolve(PublishOnFacebookPlatformUseCase);
            const updateResults = await publishOnFacebookUseCase.publishFields({
                credentialId: 'azerty',
                pageId: '12345',
                dataToPublish: {
                    is_owned: false,
                    category: 'restaurant allemand',
                },
                restaurantId: '123',
            });
            expect(updateResults).toEqual({
                errors: [
                    { field: 'isClaimed', reason: InvalidPlatformReason.UNKNOWN },
                    { field: 'category', reason: InvalidPlatformReason.UNKNOWN },
                ],
            });
        });

        it('should update fail', async () => {
            const publishOnFacebookUseCase = container.resolve(PublishOnFacebookPlatformUseCase);
            const updateResults = await publishOnFacebookUseCase.publishFields({
                credentialId: 'azerty',
                pageId: '12345',
                dataToPublish: {
                    about: 'small description',
                    overall_star_rating: 5,
                },
                restaurantId: '123',
            });
            expect(updateResults).toEqual({
                errors: [
                    { field: 'descriptions', reason: InvalidPlatformReason.UNKNOWN },
                    { field: 'rating', reason: InvalidPlatformReason.UNKNOWN },
                ],
            });
        });

        it('should update partially succeed', async () => {
            const publishOnFacebookUseCase = container.resolve(PublishOnFacebookPlatformUseCase);
            const updateResults = await publishOnFacebookUseCase.publishFields({
                credentialId: 'azerty',
                pageId: '12345',
                dataToPublish: {
                    about: 'small description',
                    name: 'les copains suisses',
                },
                restaurantId: '123',
            });
            expect(updateResults).toEqual({
                errors: [{ field: 'descriptions', reason: InvalidPlatformReason.UNKNOWN }],
            });
        });
    });

    describe('PublishOnFacebookPlatformUseCase', () => {
        registerRepositories(['RestaurantsRepository', 'CategoriesRepository', 'PlatformsRepository', 'CredentialsRepository']);

        it('should not send field delivery_and_pickup_option_info if the main category of the restaurant is "Caterer"', async () => {
            const testCase = new TestCaseBuilderV2({
                seeds: {
                    categories: {
                        data() {
                            return [
                                getDefaultCategory().platformKey(PlatformKey.GMB).categoryId(GmbCategoryIdEnum.CATERING_SERVICE).build(),
                            ];
                        },
                    },
                    credentials: {
                        data() {
                            return [getDefaultCredential().build()];
                        },
                    },
                    restaurants: {
                        data(deps) {
                            return [
                                getDefaultRestaurant().category(deps.categories()[0]._id).categoryList([deps.categories()[0]._id]).build(),
                            ];
                        },
                    },
                    platforms: {
                        data(deps) {
                            return [
                                getDefaultPlatform()
                                    .credentials([deps.credentials()[0]._id])
                                    .restaurantId(deps.restaurants()[0]._id)
                                    .key(PlatformKey.FACEBOOK)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seeds = testCase.getSeededObjects();

            const restaurantsRepository = container.resolve(RestaurantsRepository);
            const restaurantId = seeds.restaurants[0]._id;
            const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [
                        { path: 'category' },
                        { path: 'logo' },
                        { path: 'cover' },
                        { path: 'categoryList' },
                        { path: 'attributeList', populate: [{ path: 'attribute' }] },
                    ],
                },
            })) as RestaurantPopulatedToPublish;

            const publishOnFacebookPlatformUseCase = container.resolve(PublishOnFacebookPlatformUseCase);
            jest.spyOn(publishOnFacebookPlatformUseCase, 'publishFields');

            await publishOnFacebookPlatformUseCase.execute({
                restaurant,
                keysToUpdate: Object.keys(restaurant) as (keyof RestaurantPopulatedToPublish)[],
            });

            expect(publishOnFacebookPlatformUseCase.publishFields).toHaveBeenCalledWith(
                expect.objectContaining({
                    dataToPublish: expect.objectContaining({
                        category_list: expect.arrayContaining([FacebookCategoryIdEnum.CATERER]),
                    }),
                })
            );

            expect(publishOnFacebookPlatformUseCase.publishFields).toHaveBeenCalledWith(
                expect.not.objectContaining({
                    dataToPublish: expect.objectContaining({
                        delivery_and_pickup_option_info: expect.toBeArray(),
                    }),
                })
            );
        });
    });
});
