import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { isNotNil, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { UberEatsApiProviderUseCases } from ':modules/credentials/platforms/ubereats/ubereats.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { UbereatsMapper } from ':modules/platforms/platforms/ubereats/ubereats-mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { UberEatsHolidayHours, UberEatsStoreInfoInput } from ':providers/ubereats/ubereats.provider.interfaces';

@singleton()
export class PublishOnUberEatsPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _uberEatsApiProviderUseCases: UberEatsApiProviderUseCases
    ) {}

    async execute({ restaurant }: { restaurant: RestaurantPopulatedToPublish }): Promise<PublishOnPlatformResponse> {
        const restaurantId = restaurant._id.toString();
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.UBEREATS);

        if (!platform) {
            logger.warn('[UBEREATS PUBLISH] Platform not found', { restaurantId });
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
        }

        const storeId = platform.socialId;
        assert(storeId, 'Missing storeId on platform');
        const mapper = new UbereatsMapper();
        const dataToPublish = mapper.toPlatformMapper(restaurant);
        const { holiday_hours: holidayHours, name, location, contact } = dataToPublish;

        const promises: Promise<NonNullable<PublishOnPlatformResponse['errors']>[0] | undefined>[] = [];
        if (holidayHours) {
            promises.push(this._setStoreHolidayHours({ restaurantId, storeId, holidayHours }));
        }

        if (location || contact) {
            promises.push(
                this._setStoreInfo({
                    restaurantId,
                    storeId,
                    info: {
                        name,
                        location,
                        contact,
                    },
                })
            );
        }

        const errors: PublishOnPlatformResponse['errors'] = (await Promise.all(promises)).filter(isNotNil);
        return {
            success: errors.length === 0,
            errors,
        };
    }

    private async _setStoreHolidayHours({
        restaurantId,
        storeId,
        holidayHours,
    }: {
        restaurantId: string;
        storeId: string;
        holidayHours: UberEatsHolidayHours;
    }): Promise<NonNullable<PublishOnPlatformResponse['errors']>[0] | undefined> {
        try {
            await this._uberEatsApiProviderUseCases.setHolidayHours({ storeId, holidayHours });
            logger.info('[UBEREATS PUBLISH] Updated info', { restaurantId, storeId, field: 'holidayHours', value: holidayHours });
        } catch (err: any) {
            logger.error('[UBEREATS PUBLISH] Error updating info', {
                restaurantId,
                storeId,
                field: 'holidayHours',
                value: holidayHours,
                error: err,
            });

            return {
                field: 'holidayHours',
                reason: err.message ?? 'Error setting holidayHours',
            };
        }
    }

    private async _setStoreInfo({
        restaurantId,
        storeId,
        info,
    }: {
        restaurantId: string;
        storeId: string;
        info: UberEatsStoreInfoInput;
    }): Promise<NonNullable<PublishOnPlatformResponse['errors']>[0] | undefined> {
        try {
            await this._uberEatsApiProviderUseCases.setStoreInfo({ storeId, info });
            logger.info('[UBEREATS PUBLISH] Updated info', { restaurantId, storeId, field: 'info', info });
        } catch (err: any) {
            logger.error('[UBEREATS PUBLISH] Error updating info', {
                restaurantId,
                storeId,
                field: 'info',
                info,
                error: err,
            });

            return {
                field: 'info',
                reason: err.message ?? 'Error setting store info',
            };
        }
    }
}
