import { singleton } from 'tsyringe';

import { IInformationUpdate, toDbId } from '@malou-io/package-models';
import {
    configInformationUpdateSupportedKeys,
    getPlatformDefinition,
    getPlatformKeysForYextPublisherIds,
    InformationUpdateErrorReason,
    InformationUpdateErrorReasonMapping,
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    InformationUpdateSupportedPlatformKey,
    isNotNil,
    MalouErrorCode,
    PlatformAccessStatus,
    PlatformAccessType,
    PlatformKey,
    platformKeyToProvider,
    platformKeyToSupportedPlatformKey,
    YextPublisherId,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GetSupportedFieldsForPlatformService } from ':modules/information-updates/services/get-supported-fields-for-platform.service';
import {
    PublishOnConnectedPlatformsUseCase,
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import YextListingService from ':modules/publishers/yext/services/yext-listing.service';
import UpdateLocationUseCase from ':modules/publishers/yext/use-cases/update-location/update-location.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { YextListingStatus } from ':providers/yext/yext.provider.interfaces';
import { SlackService } from ':services/slack.service';

@singleton()
export class StartUpdateOnPlatformsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _publishOnConnectedPlatformsUseCase: PublishOnConnectedPlatformsUseCase,
        private readonly _yextListingService: YextListingService,
        private readonly _updateLocationUseCase: UpdateLocationUseCase,
        private readonly _getSupportedFieldsForPlatformService: GetSupportedFieldsForPlatformService,
        private readonly _slackService: SlackService
    ) {}

    async execute(informationUpdate: IInformationUpdate): Promise<{
        platformStates: IInformationUpdate['platformStates'];
    }> {
        const restaurantId = informationUpdate.restaurantId.toString();
        const restaurant: RestaurantPopulatedToPublish = (await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: {
                lean: true,
                populate: [
                    { path: 'category' },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'categoryList' },
                    { path: 'attributeList', populate: [{ path: 'attribute' }] },
                ],
            },
        })) as RestaurantPopulatedToPublish;

        logger.info(`[PUBLISH INFOS] About to update data`, {
            informationUpdateId: informationUpdate._id,
            restaurantId,
            data: informationUpdate.data,
        });

        const platformStates = await this._updateLocation(restaurant, informationUpdate);

        // temp fix because google return 500 when updating media (but it works internally) so we never re-set these boolean to false
        await this._restaurantsRepository.findOneAndUpdate({
            filter: { _id: restaurantId },
            update: { logoChanged: false, coverChanged: false },
            options: { lean: true },
        });

        return { platformStates };
    }

    private _informationUpdatesToRestaurantKeys(informationUpdateData: IInformationUpdate['data']): (keyof RestaurantPopulatedToPublish)[] {
        return Object.keys(informationUpdateData)
            .map((key) => {
                if (key === 'latlng') {
                    return undefined;
                }

                if (key === 'secondaryCategoriesNames') {
                    return 'categoryList';
                }

                if (key === 'longDescription' || key === 'shortDescription') {
                    return 'descriptions';
                }

                if (key === 'categoryName') {
                    return 'category';
                }

                if (key === 'attributes') {
                    return 'attributeList';
                }

                return key;
            })
            .filter(isNotNil) as (keyof RestaurantPopulatedToPublish)[];
    }

    private async _updateLocation(
        restaurant: RestaurantPopulatedToPublish,
        informationUpdate: IInformationUpdate
    ): Promise<IInformationUpdate['platformStates']> {
        try {
            const yextAndMalouPlatformStates = await this._updateYextPublishersAndOtherPlatforms(restaurant, informationUpdate);

            return yextAndMalouPlatformStates;
        } catch (error: any) {
            if (error instanceof MalouError && [MalouErrorCode.YEXT_NOT_SUPPORTED].includes(error.malouErrorCode)) {
                logger.warn(`[PUBLISH INFOS] [YEXT] Yext not supported, falling back to custom update`, {
                    restaurantId: restaurant._id.toString(),
                    informationUpdateId: informationUpdate._id,
                });
            } else {
                logger.error(`[PUBLISH INFOS] [YEXT] Yext update failed`, {
                    restaurantId: restaurant._id.toString(),
                    informationUpdateId: informationUpdate._id,
                    error,
                });

                // Send Slack alert
                await this._slackService.sendMessage({
                    text: await this._formatSlackMessage({
                        errorMessage: error.message,
                        restaurantId: restaurant._id.toString(),
                    }),
                });
            }

            const malouPlatformStates = await this._publishUpdatesOnMalouPlatforms({
                restaurant,
                informationUpdate,
            });

            return malouPlatformStates;
        }
    }

    private async _updateYextPublishersAndOtherPlatforms(
        restaurant: RestaurantPopulatedToPublish,
        informationUpdate: IInformationUpdate
    ): Promise<IInformationUpdate['platformStates']> {
        const platformStates: IInformationUpdate['platformStates'] = [];
        const restaurantId = restaurant._id.toString();

        await this._updateLocationUseCase.execute(restaurant);

        const yextListings = await this._yextListingService.getListingsForRestaurant(restaurantId);

        const yextPlatformStates = yextListings
            // Filter listings that will effectively update the requested fields
            .filter((listing) => {
                const key = configInformationUpdateSupportedKeys[listing.publisherId as YextPublisherId]?.name;

                if (!key) {
                    return false;
                }

                const provider = InformationUpdateProvider.YEXT;
                const providerKey = platformKeyToProvider[provider][key];
                const willUpdateData = this._getSupportedFieldsForPlatformService.willUpdateData({
                    data: informationUpdate.data,
                    providerKey,
                    provider,
                });

                if (!willUpdateData) {
                    logger.info(`[PUBLISH INFOS] Yext listing will not update any data`, {
                        informationUpdateId: informationUpdate._id,
                        restaurantId,
                        data: informationUpdate.data,
                        platformKey: key,
                    });
                }

                return willUpdateData;
            })
            .map((listing) => {
                const status =
                    listing.status === YextListingStatus.LIVE
                        ? InformationUpdatePlatformStateStatus.DONE
                        : InformationUpdatePlatformStateStatus.PENDING;
                const key = configInformationUpdateSupportedKeys[listing.publisherId as YextPublisherId]?.name;
                const now = new Date();

                return {
                    key,
                    status,
                    provider: InformationUpdateProvider.YEXT,
                    yextStatus: listing.status,
                    updateDoneAt: status === InformationUpdatePlatformStateStatus.DONE ? now : null,
                };
            });
        platformStates.push(...yextPlatformStates);

        logger.info(`[PUBLISH INFOS] [YEXT] Updated data using Yext`, {
            restaurantId,
            informationUpdateId: informationUpdate._id,
            yextPlatformStates,
        });

        const yextAvailablePlatformIds = yextListings.map((listing) => listing.publisherId);
        const platformKeysAlreadyUpdatedByYext = getPlatformKeysForYextPublisherIds(yextAvailablePlatformIds);

        const malouPlatformStates = await this._publishUpdatesOnMalouPlatforms({
            restaurant,
            informationUpdate,
            filterPlatformKey: (platformKey) => !!platformKey && !platformKeysAlreadyUpdatedByYext.includes(platformKey),
        });
        platformStates.push(...malouPlatformStates);

        return platformStates;
    }

    private async _publishUpdatesOnMalouPlatforms({
        restaurant,
        informationUpdate,
        filterPlatformKey,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        informationUpdate: IInformationUpdate;
        filterPlatformKey?: (platformKey?: PlatformKey) => boolean;
    }): Promise<IInformationUpdate['platformStates']> {
        const platformStates: IInformationUpdate['platformStates'] = [];
        const shouldKeepPlatformKey = filterPlatformKey ?? (() => true);
        const keysToUpdate = this._informationUpdatesToRestaurantKeys(informationUpdate.data);

        const restaurantAccess = restaurant.access
            .filter((access) => access.active && getPlatformDefinition(access.platformKey)?.shouldCompareInformation)
            .filter(({ platformKey }) => isNotNil(platformKeyToSupportedPlatformKey[platformKey]))
            .filter(({ platformKey }) => shouldKeepPlatformKey(platformKey))
            .filter(({ platformKey }) =>
                this._willUpdateDataThroughMalou({
                    platformKey,
                    restaurant,
                    informationUpdate,
                })
            );

        // Get all auto platforms not updated by Yext yet
        const autoPlatformKeys = restaurantAccess
            .filter((access) => access.accessType === PlatformAccessType.AUTO)
            .map((access) => access.platformKey);

        logger.info(`[PUBLISH INFOS] About to update data on Malou Platforms`, {
            restaurantId: restaurant._id,
            informationUpdateId: informationUpdate._id,
            platforms: autoPlatformKeys,
        });

        if (
            restaurant.appleBusinessConnect?.locationId &&
            isNotNil(platformKeyToSupportedPlatformKey[PlatformKey.ABC]) &&
            shouldKeepPlatformKey(PlatformKey.ABC) &&
            this._willUpdateDataThroughMalou({
                platformKey: PlatformKey.ABC,
                restaurant,
                informationUpdate,
            })
        ) {
            void this._publishOnConnectedPlatformsUseCase.execute({ platformKey: PlatformKey.ABC, restaurant, keysToUpdate }).catch(() =>
                logger.error(`[PUBLISH INFOS] Error updating on ABC`, {
                    restaurantId: restaurant._id,
                    informationUpdateId: informationUpdate._id,
                    platforms: autoPlatformKeys,
                })
            );
        }

        const autoPlatformStates = await Promise.all(
            autoPlatformKeys.map(async (platformKey) => {
                const mappedPlatformKey = platformKeyToSupportedPlatformKey[platformKey];
                const response = await this._publishOnConnectedPlatformsUseCase.execute({
                    platformKey,
                    restaurant,
                    keysToUpdate,
                });

                return this._mapUpdateResponseToInformationUpdatePlatformState({
                    platformKey: mappedPlatformKey,
                    provider: InformationUpdateProvider.MALOU,
                    response,
                });
            })
        );
        platformStates.push(...autoPlatformStates.filter(isNotNil));

        logger.info(`[PUBLISH INFOS] Updated data on Malou platforms`, {
            restaurantId: restaurant._id,
            informationUpdateId: informationUpdate._id,
            platformStates,
        });

        // Get all non-auto platforms not updated by Yext yet
        const manualRestaurantAccess = restaurantAccess.filter((access) => access.accessType !== PlatformAccessType.AUTO);

        manualRestaurantAccess.forEach((access) => {
            const key = platformKeyToSupportedPlatformKey[access.platformKey];
            platformStates.push({
                key,
                status: this._mapManualPlatformAccessStatusToInformationUpdatePlatformStateStatus(access.status),
                provider: InformationUpdateProvider.MALOU,
            });
        });

        logger.info(`[PUBLISH INFOS] Created malou manual update`, {
            restaurantId: restaurant._id,
            informationUpdateId: informationUpdate._id,
            platforms: manualRestaurantAccess,
        });

        return platformStates;
    }

    private _mapUpdateResponseToInformationUpdatePlatformState({
        platformKey,
        response,
        provider,
    }: {
        platformKey: InformationUpdateSupportedPlatformKey;
        response: PublishOnPlatformResponse;
        provider: InformationUpdateProvider;
    }): IInformationUpdate['platformStates'][0] | undefined {
        const status = !response.success ? InformationUpdatePlatformStateStatus.ERROR : InformationUpdatePlatformStateStatus.DONE;
        const now = new Date();
        const errors = response.errors?.filter(isNotNil);

        return {
            key: platformKey,
            status,
            provider,
            ...(status === InformationUpdatePlatformStateStatus.DONE && { updateDoneAt: now }),
            ...(errors &&
                errors.length > 0 && {
                    errors: errors.map(({ field, reason }) => ({
                        field,
                        reason: InformationUpdateErrorReasonMapping[reason] || InformationUpdateErrorReason.UNKNOWN,
                    })),
                }),
        };
    }

    private _willUpdateDataThroughMalou({
        platformKey,
        restaurant,
        informationUpdate,
    }: {
        platformKey: PlatformKey;
        restaurant: RestaurantPopulatedToPublish;
        informationUpdate: IInformationUpdate;
    }): boolean {
        const provider = InformationUpdateProvider.MALOU;
        const providerKey = platformKeyToProvider[provider][platformKey];
        const willUpdateData = this._getSupportedFieldsForPlatformService.willUpdateData({
            data: informationUpdate.data,
            providerKey,
            provider,
        });

        if (!willUpdateData) {
            logger.info(`[PUBLISH INFOS] Malou platform will not update any data`, {
                informationUpdateId: informationUpdate._id,
                restaurantId: restaurant._id,
                data: informationUpdate.data,
                platformKey,
            });
        }

        return willUpdateData;
    }

    private _mapManualPlatformAccessStatusToInformationUpdatePlatformStateStatus(
        status: PlatformAccessStatus
    ): InformationUpdatePlatformStateStatus {
        switch (status) {
            case PlatformAccessStatus.BAD_ACCESS:
                return InformationUpdatePlatformStateStatus.BAD_ACCESS;
            case PlatformAccessStatus.UNCLAIMED_PAGE:
                return InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE;
            case PlatformAccessStatus.INVALID_PAGE:
                return InformationUpdatePlatformStateStatus.INVALID_PAGE;
            case PlatformAccessStatus.FAILED:
            case PlatformAccessStatus.MISSING_PERMISSIONS:
            case PlatformAccessStatus.UNVERIFIED:
            case PlatformAccessStatus.NOT_FOUND:
                return InformationUpdatePlatformStateStatus.ERROR;
            default:
                return InformationUpdatePlatformStateStatus.PENDING;
        }
    }

    private async _formatSlackMessage({ errorMessage, restaurantId }: { errorMessage: string; restaurantId: string }): Promise<string> {
        const context = await this._slackService.createContextForSlack({ restaurantId });
        const restaurantInfo = `\nOn Yext`;

        return `*Error while updating restaurant*${restaurantInfo}\n>*error:* ${errorMessage}${context}`;
    }
}
