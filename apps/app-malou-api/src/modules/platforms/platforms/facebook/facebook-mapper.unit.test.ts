import phoneUtil from 'google-libphonenumber';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { Day } from '@malou-io/package-utils';

import { FacebookMapper } from ':modules/platforms/platforms/facebook/facebook-mapper';

describe('FacebookMapper', () => {
    describe('Google Libphonenumber', () => {
        const instance = phoneUtil.PhoneNumberUtil.getInstance();
        it('should parse phone numbers correctly', () => {
            let number = instance.parseAndKeepRawInput('+34 972 222 157', 'ES');
            expect(number.getCountryCode()).toBe(34);
            expect(number.getNationalNumber()).toBe(972222157);
            number = instance.parseAndKeepRawInput('(*************', 'US');
            expect(number.getCountryCode()).toBe(1);
            expect(number.getNationalNumber()).toBe(7183877400);
            number = instance.parseAndKeepRawInput('(*************', 'FR');
            expect(number.getCountryCode()).toBe(33);
            expect(number.getNationalNumber()).toBe(7183877400);
            number = instance.parseAndKeepRawInput('01 48 76 53 19', 'FR');
            expect(number.getCountryCode()).toBe(33);
            expect(number.getNationalNumber()).toBe(148765319);
            expect(() => {
                number = instance.parseAndKeepRawInput('01 48 76 53 19', 'FRR');
            }).toThrow();
        });
    });

    describe('toPlatformMapper', () => {
        it('should return empty object when input is empty object', async () => {
            const malouData = { _id: newDbId() };

            const mapper = container.resolve(FacebookMapper);
            const result = await mapper.toPlatformMapper(malouData);

            expect(result).toEqual({});
        });

        it('should return facebook regular hours format', async () => {
            const malouData = {
                _id: newDbId(),
                regularHours: [
                    {
                        openDay: Day.MONDAY,
                        openTime: '19:00',
                        closeDay: Day.MONDAY,
                        closeTime: '22:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.TUESDAY,
                        openTime: '19:00',
                        closeDay: Day.TUESDAY,
                        closeTime: '22:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.TUESDAY,
                        openTime: '12:00',
                        closeDay: Day.TUESDAY,
                        closeTime: '14:00',
                        isClosed: false,
                        isPrimaryPeriod: false,
                    },
                    {
                        openDay: Day.WEDNESDAY,
                        openTime: '12:00',
                        closeDay: Day.WEDNESDAY,
                        closeTime: '14:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.WEDNESDAY,
                        openTime: '19:00',
                        closeDay: Day.WEDNESDAY,
                        closeTime: '22:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.THURSDAY,
                        openTime: '12:00',
                        closeDay: Day.THURSDAY,
                        closeTime: '14:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.THURSDAY,
                        openTime: '19:00',
                        closeDay: Day.THURSDAY,
                        closeTime: '22:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.FRIDAY,
                        openTime: '12:00',
                        closeDay: Day.FRIDAY,
                        closeTime: '14:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.FRIDAY,
                        openTime: '19:00',
                        closeDay: Day.FRIDAY,
                        closeTime: '22:30',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.SATURDAY,
                        openTime: '12:00',
                        closeDay: Day.SATURDAY,
                        closeTime: '15:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.SATURDAY,
                        openTime: '19:00',
                        closeDay: Day.SATURDAY,
                        closeTime: '22:30',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                    {
                        openDay: Day.SUNDAY,
                        openTime: '12:00',
                        closeDay: Day.SUNDAY,
                        closeTime: '15:00',
                        isClosed: false,
                        isPrimaryPeriod: true,
                    },
                ],
            };

            const mapper = container.resolve(FacebookMapper);
            const result = await mapper.toPlatformMapper(malouData);

            const expectedResult = {
                hours: {
                    mon_1_open: '19:00',
                    mon_1_close: '22:00',
                    tue_1_open: '12:00',
                    tue_1_close: '14:00',
                    tue_2_open: '19:00',
                    tue_2_close: '22:00',
                    wed_1_open: '12:00',
                    wed_1_close: '14:00',
                    wed_2_open: '19:00',
                    wed_2_close: '22:00',
                    thu_1_open: '12:00',
                    thu_1_close: '14:00',
                    thu_2_open: '19:00',
                    thu_2_close: '22:00',
                    fri_1_open: '12:00',
                    fri_1_close: '14:00',
                    fri_2_open: '19:00',
                    fri_2_close: '22:30',
                    sat_1_open: '12:00',
                    sat_1_close: '15:00',
                    sat_2_open: '19:00',
                    sat_2_close: '22:30',
                    sun_1_open: '12:00',
                    sun_1_close: '15:00',
                },
            };
            expect(result).toEqual(expectedResult);
        });
    });
});
