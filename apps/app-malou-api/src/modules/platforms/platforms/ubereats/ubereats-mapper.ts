import lodash from 'lodash';
import { DateTime } from 'luxon';

import { IRestaurant } from '@malou-io/package-models';
import { CountryCode } from '@malou-io/package-utils';

import { getCountryNameFrFromCountryCode } from ':helpers/utils';
import { Mapper } from ':modules/platforms/platforms.mapper';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import { SpecialHour } from ':modules/restaurants/use-cases/duplicate-special-hours.use-case';
import {
    UberEatsHolidayHours,
    UberEatsStoreContact,
    UberEatsStoreLocation,
    UberEatsStoreWithHolidayHoursAndStatus,
} from ':providers/ubereats/ubereats.provider.interfaces';

export class UbereatsMapper extends Mapper<any, any> {
    supportedUpdateFields: string[] = [];

    constructor() {
        super();
        this.setSupportedFields();
        this.setSupportedUpdateFields();
    }

    setSupportedFields(): void {
        this.supportedFields = Object.keys(UbereatsMapper.brain);
    }

    setSupportedUpdateFields(): void {
        this.supportedUpdateFields = Object.values(UbereatsMapper.brain)
            .filter((v: any) => !!v.shouldBePublished)
            .map((v) => v.origin);
    }

    toMalouMapper(platData: Partial<UberEatsStoreWithHolidayHoursAndStatus>): any {
        const final = {};
        for (const f of this.supportedFields) {
            const mapped = UbereatsMapper.brain[f];
            final[mapped.origin] = 'originFn' in mapped ? mapped.originFn?.(platData) : mapped.target ? platData[mapped.target] : undefined;
        }
        return final;
    }

    toPlatformMapper(malouData: Partial<RestaurantPopulatedToPublish>): any {
        const res = {};
        for (const f of this.supportedUpdateFields) {
            const mapped = UbereatsMapper.brain[f];
            if (mapped.target) {
                res[mapped.target] = 'targetFn' in mapped ? mapped.targetFn?.(malouData) : malouData[mapped.origin];
            }
        }
        return lodash.omitBy(res, lodash.isNil);
    }

    toMalouMapperSearch(store: UberEatsStoreWithHolidayHoursAndStatus): MalouRestaurantSearchResult {
        return {
            socialId: store.id,
            name: store.name ?? null,
            formattedAddress: store.location ? store.location.street_address_line_one : undefined,
        };
    }

    getPlatformFieldName(malouField: string): string | undefined {
        if (!this.isSupportedField(malouField)) {
            return;
        }
        return UbereatsMapper.brain[malouField]?.target;
    }

    static get brain(): Record<
        string,
        {
            origin: string;
            target?: string;
            originFn?: (data: Partial<UberEatsStoreWithHolidayHoursAndStatus>) => any;
            targetFn?: (el: Partial<RestaurantPopulatedToPublish>) => any;
            shouldBePublished?: boolean;
        }
    > {
        return {
            name: {
                origin: 'name',
                target: 'name',
                shouldBePublished: true,
            },
            address: {
                origin: 'address',
                originFn: (data: Partial<UberEatsStoreWithHolidayHoursAndStatus>): IRestaurant['address'] => {
                    if (!data.location) {
                        return null;
                    }
                    const { street_address_line_one: streetAddressLineOne, city, country, postal_code: postalCode } = data.location;
                    if (!country || !city) {
                        return null;
                    }
                    const countryName = getCountryNameFrFromCountryCode(country);
                    if (!countryName) {
                        return null;
                    }
                    return {
                        country: countryName,
                        regionCode: country as CountryCode,
                        locality: city,
                        postalCode,
                        formattedAddress: streetAddressLineOne,
                    };
                },
                target: 'location',
                targetFn: (el: Partial<RestaurantPopulatedToPublish>): UberEatsStoreLocation | null => {
                    if (!el.address) {
                        return null;
                    }
                    const lat = el.latlng?.lat;
                    const lng = el.latlng?.lng;
                    if (!lat || !lng) {
                        return null;
                    }
                    return {
                        street_address_line_one: el.address.formattedAddress,
                        city: el.address.locality,
                        country: el.address.regionCode,
                        postal_code: el.address.postalCode,
                        latitude: lat.toString(),
                        longitude: lng.toString(),
                    };
                },
                shouldBePublished: true,
            },
            latlng: {
                origin: 'latlng',
                originFn: (data: Partial<UberEatsStoreWithHolidayHoursAndStatus>): IRestaurant['latlng'] | null => {
                    if (!data.location) {
                        return null;
                    }
                    const { latitude: lat, longitude: lng } = data.location;
                    return {
                        lat: parseFloat(lat),
                        lng: parseFloat(lng),
                    };
                },
            },
            phone: {
                origin: 'phone',
                target: 'contact',
                targetFn: (el: Partial<RestaurantPopulatedToPublish>): UberEatsStoreContact | undefined => {
                    if (!el.phone || !el.phone.digits || !el.phone.prefix) {
                        return;
                    }

                    return {
                        phone_number: `+${el.phone.prefix}${el.phone.digits}`,
                    };
                },
                shouldBePublished: true,
            },
            specialHours: {
                origin: 'specialHours',
                originFn: (data: Partial<UberEatsStoreWithHolidayHoursAndStatus>): IRestaurant['specialHours'] => {
                    if (!data.holiday_hours || !Object.keys(data.holiday_hours)) {
                        return [];
                    }
                    return this.mapSpecialHours(data);
                },
                target: 'holiday_hours',
                targetFn: (restaurant: Partial<RestaurantPopulatedToPublish>): UberEatsHolidayHours | null => {
                    if (!restaurant.specialHours || restaurant.specialHours.length === 0) {
                        return null;
                    }

                    const futureSpecialHours = restaurant.specialHours.filter(
                        (specialHour) =>
                            DateTime.now().startOf('day') <=
                            DateTime.fromObject({ ...specialHour.endDate, month: specialHour.endDate.month + 1 }).startOf('day')
                    ); // We add 1 to the endDate month because Luxon first month is 1 and our first month is 0

                    if (futureSpecialHours.length === 0) {
                        return null;
                    }

                    const uberEatsSpecialHours: UberEatsHolidayHours = {};

                    futureSpecialHours.forEach((specialHour) => {
                        const { openTime, isClosed, closeTime: originalCloseTime } = specialHour;

                        const startDate = specialHour.startDate;
                        const endDate = specialHour.endDate;

                        const dateKey1 = startDate
                            ? DateTime.fromObject({ day: startDate.day, month: startDate.month + 1, year: startDate.year })
                                  .startOf('day')
                                  .toFormat('yyyy-MM-dd')
                            : undefined;

                        const dateKey2 = endDate
                            ? DateTime.fromObject({ day: endDate.day, month: endDate.month + 1, year: endDate.year })
                                  .startOf('day')
                                  .toFormat('yyyy-MM-dd')
                            : undefined;

                        if (dateKey1 && !uberEatsSpecialHours[dateKey1]) {
                            uberEatsSpecialHours[dateKey1] = {
                                open_time_periods: [],
                            };
                        }

                        if (dateKey2 && !uberEatsSpecialHours[dateKey2]) {
                            uberEatsSpecialHours[dateKey2] = {
                                open_time_periods: [],
                            };
                        }

                        const closeTime = originalCloseTime === '24:00' ? '23:59' : originalCloseTime;

                        if (dateKey1 && dateKey1 === dateKey2) {
                            if (isClosed) {
                                uberEatsSpecialHours[dateKey1].open_time_periods.push({
                                    start_time: '00:00',
                                    end_time: '00:00',
                                });
                            } else if (openTime && closeTime) {
                                uberEatsSpecialHours[dateKey1].open_time_periods.push({
                                    start_time: openTime,
                                    end_time: closeTime,
                                });
                            }
                        } else if (dateKey1 && dateKey2) {
                            if (isClosed) {
                                uberEatsSpecialHours[dateKey1].open_time_periods.push({
                                    start_time: '00:00',
                                    end_time: '00:00',
                                });
                                uberEatsSpecialHours[dateKey2].open_time_periods.push({
                                    start_time: '00:00',
                                    end_time: '00:00',
                                });
                            } else if (openTime && closeTime) {
                                uberEatsSpecialHours[dateKey1].open_time_periods.push({
                                    start_time: openTime,
                                    end_time: '23:59',
                                });
                                uberEatsSpecialHours[dateKey2].open_time_periods.push({
                                    start_time: '00:00',
                                    end_time: closeTime,
                                });
                            }
                        }
                    });

                    // Filter future special hours
                    // We have to keep this filter because a date can be processed because endDate is after today, but startDate is before today.
                    const today = DateTime.now().startOf('day');
                    return Object.fromEntries(
                        Object.entries(uberEatsSpecialHours).filter(([key]) => {
                            const date = DateTime.fromISO(key);
                            return date.isValid && date >= today;
                        })
                    );
                },
                shouldBePublished: true,
            },
        };
    }

    static mapSpecialHours(data: Partial<UberEatsStoreWithHolidayHoursAndStatus>): SpecialHour[] {
        const specialHours: SpecialHour[] = [];
        const holidaysHours = data.holiday_hours;

        if (!holidaysHours) {
            return specialHours;
        }

        for (const [key, value] of Object.entries(holidaysHours)) {
            const [year, month, day] = key.split('-');
            (value as any).open_time_periods.forEach((otp) => {
                const specialHour = {
                    startDate: {
                        day: +day,
                        year: +year,
                        month: +month - 1,
                    },
                    endDate: {
                        day: +day,
                        year: +year,
                        month: +month - 1,
                    },
                    openTime: otp.start_time,
                    closeTime: otp.end_time,
                    isClosed: false,
                };
                // https://developer.uber.com/docs/eats/api/v1/post-eats-stores-storeid-holidayhours#request-holiday-hour
                // "If a restaurant is closed for the whole day, set the start_time and end_time to 00: 00."
                if (otp.start_time === '00:00' && otp.end_time === '00:00') {
                    specialHour.openTime = null;
                    specialHour.closeTime = null;
                    specialHour.isClosed = true;
                }
                specialHours.push(specialHour);
            });
        }
        return specialHours;
    }
}
