import clm from 'country-locale-map';
import { isNil, omit, pick, uniq } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IRestaurant, IRestaurantAttributeWithAttribute, toDbIds } from '@malou-io/package-models';
import {
    createDateFromMalouDate,
    Day,
    DescriptionSize,
    isAfterToday,
    isNotNil,
    Locale,
    PlatformKey,
    RestaurantAttributeValue,
    SocialNetworkKey,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { filterByRequiredKeys, NonNullableKeys } from ':helpers/validators/filter-by-required-keys';
import { HourTypesRepository } from ':modules/hour-types/hour-types.repository';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import {
    AppleBusinessConnectLocationAttribute,
    AppleBusinessConnectLocationDay,
    AppleBusinessConnectLocationDescriptions,
    AppleBusinessConnectLocationDetails,
    AppleBusinessConnectLocationDisplayName,
    AppleBusinessConnectLocationHoursByDay,
    AppleBusinessConnectLocationKeywords,
    AppleBusinessConnectLocationPaymentMethods,
    AppleBusinessConnectLocationPhoneNumber,
    AppleBusinessConnectLocationPhoneNumberType,
    AppleBusinessConnectLocationPoint,
    AppleBusinessConnectLocationPointSource,
    AppleBusinessConnectLocationServiceHours,
    AppleBusinessConnectLocationSpecialHours,
    AppleBusinessConnectLocationStatus,
    AppleBusinessConnectLocationStatusWithDates,
    AppleBusinessConnectLocationTextLocaleValue,
    AppleBusinessConnectLocationUrl,
    AppleBusinessConnectLocationUrlType,
} from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.interfaces';
import { appleBusinessConnectAttributesMapping } from ':modules/platforms/platforms/apple-business-connect/mappings/attributes.mapping';
import { appleBusinessConnectCategoriesMapping } from ':modules/platforms/platforms/apple-business-connect/mappings/categories.mapping';
import { appleBusinessConnectPaymentMapping } from ':modules/platforms/platforms/apple-business-connect/mappings/payment.mapping';
import { appleBusinessConnectServiceHoursMapping } from ':modules/platforms/platforms/apple-business-connect/mappings/service-hours.mapping';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { Translation } from ':services/translation.service';

export interface IRestaurantWithAttributesList extends IRestaurant {
    attributeList: IRestaurantAttributeWithAttribute[];
}

export interface IPhone {
    digits: number;
    prefix: number;
}

@singleton()
export class AppleBusinessConnectMapperService {
    private readonly _APPLE_BUSINESS_CONNECT_MAX_DESCRIPTION_LENGTH = 500;
    private readonly _MAX_KEYWORDS_COUNT = 30;
    private readonly _DEFAULT_REGION_CODE = 'fr-FR';

    // We remove displayNames from the mapper because Apple doesn't like when location are present in the name
    // aka: they prefer Bolkiri Batignolles to Bolkiri alone, but we're unable to clean the name for now
    private readonly _APPLE_KEYS_TO_REMOVE = ['displayNames'];

    constructor(
        private readonly _hourTypesRepository: HourTypesRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _keywordsRepository: KeywordsTempRepository,
        private readonly _translationService: Translation
    ) {}

    async execute({
        restaurant,
        keysToUpdate,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate?: (keyof RestaurantPopulatedToPublish)[];
    }): Promise<Partial<AppleBusinessConnectLocationDetails>> {
        const locale = this._getLocaleFromRestaurantCountry(restaurant);
        const displayNames = this._getLocationDetailsDisplayNames(restaurant);
        const address = this._getLocationDetailsAddress(restaurant);
        const locationStatus = this._getLocationDetailsStatus(restaurant);
        const categories = this._getCategories(restaurant);
        const openingHoursByDay = this._getOpeningHoursByDay(restaurant);
        const phoneNumbers = this._getPhoneNumbers(restaurant);
        const urls = await this._getUrls(restaurant);
        const internalNicknames = restaurant.internalName ? [{ name: restaurant.internalName, locale }] : undefined;
        const locationAttributes = this._getLocationAttributes(restaurant);
        const locationDescriptions = this._getLocationDescriptions(restaurant);
        const locationKeywords = await this._getLocationKeywords(restaurant);
        const paymentMethods = this._getPaymentMethods(restaurant);
        const serviceHours = await this._getServiceHours(restaurant);
        const specialHours = this._getSpecialHours(restaurant);

        let appleRestaurant: Partial<AppleBusinessConnectLocationDetails> = {
            ...(displayNames && { displayNames }),
            ...address,
            ...(locationStatus && { locationStatus }),
            ...(categories && { categories }),
            ...(openingHoursByDay && { openingHoursByDay }),
            ...(phoneNumbers && { phoneNumbers }),
            ...(urls && { urls }),
            ...(internalNicknames && { internalNicknames }),
            ...(locationAttributes && { locationAttributes }),
            ...(locationDescriptions && { locationDescriptions }),
            ...(locationKeywords && { locationKeywords }),
            ...(paymentMethods && { paymentMethods }),
            ...(serviceHours && { serviceHours }),
            ...(specialHours && { specialHours }),
            ...(restaurant.internalName && { storeCode: restaurant.internalName }),
        };

        if (keysToUpdate) {
            const fieldsToUpdate = this._getFieldsToUpdate(keysToUpdate);
            appleRestaurant = pick(appleRestaurant, fieldsToUpdate);
        }

        return omit(appleRestaurant, this._APPLE_KEYS_TO_REMOVE);
    }

    private _getFieldsToUpdate(keysToUpdate: (keyof RestaurantPopulatedToPublish)[]): (keyof AppleBusinessConnectLocationDetails)[] {
        const fieldsMapping: Partial<Record<keyof RestaurantPopulatedToPublish, (keyof AppleBusinessConnectLocationDetails)[]>> = {
            name: ['displayNames'],
            address: ['mainAddress'],
            latlng: ['displayPoint'],
            isClosedTemporarily: ['locationStatus'],
            category: ['categories'],
            categoryList: ['categories'],
            regularHours: ['openingHoursByDay'],
            phone: ['phoneNumbers'],
            website: ['urls'],
            socialNetworkUrls: ['urls'],
            internalName: ['storeCode'],
            attributeList: ['locationAttributes', 'paymentMethods'],
            descriptions: ['locationDescriptions'],
            otherHours: ['serviceHours'],
            specialHours: ['specialHours'],
        };

        return [...uniq(keysToUpdate.map((key) => fieldsMapping[key] ?? []).flat()), 'locationKeywords', 'storeCode'];
    }

    private _getLocaleFromRestaurantCountry(restaurant: RestaurantPopulatedToPublish): string {
        const regionCode = restaurant.address?.regionCode;

        if (!regionCode) {
            logger.warn('[Apple Business Connect] No locale found for restaurant');

            return this._DEFAULT_REGION_CODE;
        }

        // https://www.npmjs.com/package/country-locale-map
        return clm.getLocaleByAlpha2(regionCode.toUpperCase())?.replace('_', '-') || this._DEFAULT_REGION_CODE;
    }

    private _getLocationDetailsDisplayNames(
        restaurant: RestaurantPopulatedToPublish
    ): AppleBusinessConnectLocationDisplayName[] | undefined {
        const locale = this._getLocaleFromRestaurantCountry(restaurant);

        if (!restaurant.name) {
            return undefined;
        }

        // Apple wants mixed case names
        const name = this._capitalizeIfAllLowerOrUpperCase(restaurant.name);
        return [{ name, locale, primary: true }];
    }

    private _capitalizeIfAllLowerOrUpperCase(str: string): string {
        // Return the original string if it's not all lowercase or uppercase
        if (str !== str.toLowerCase() && str !== str.toUpperCase()) {
            return str;
        }

        // Capitalize the first letter and concatenate the rest of the string
        return str.charAt(0).toUpperCase() + str.toLowerCase().slice(1);
    }

    private _getLocationDetailsAddress(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationPoint | undefined {
        if (!restaurant.latlng || !restaurant.address) {
            logger.warn('[Apple Business Connect] No address or coordinates found for restaurant', {
                restaurantId: restaurant._id.toString(),
            });
            return undefined;
        }

        const hasLatitudeAndLongitude = isNotNil(restaurant.latlng.lat) && isNotNil(restaurant.latlng.lng);

        return {
            ...(restaurant.address && {
                mainAddress: {
                    // TODO "VALIDATION__StructuredAddressMustIncludeComponent" field: locality must be present / Wild & The Moon – Al Serkal Avenue
                    structuredAddress: {
                        locality: restaurant.address.locality,
                        countryCode: restaurant.address.regionCode.toUpperCase(), // must be uppercase, ISO 3166 Country Codes: https://www.iso.org/iso-3166-country-codes.html https://www.iso.org/obp/ui/#search
                        administrativeArea: restaurant.address.administrativeArea ?? undefined, // required when defined by ISO for the Country
                        postCode: restaurant.address.postalCode,
                        fullThoroughfare: restaurant.address.formattedAddress,
                    },
                    locale: this._getLocaleFromRestaurantCountry(restaurant),
                },
            }),
            ...(hasLatitudeAndLongitude && {
                displayPoint: {
                    coordinates: {
                        latitude: restaurant.latlng.lat.toString(),
                        longitude: restaurant.latlng.lng.toString(),
                    },
                    source: AppleBusinessConnectLocationPointSource.CALCULATED,
                },
            }),
        };
    }

    private _getLocationDetailsStatus(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationStatusWithDates {
        if (restaurant.isClosedTemporarily) {
            return {
                status: AppleBusinessConnectLocationStatus.TEMPORARILY_CLOSED,
                // TODO: get dates
                closedDate: DateTime.local().toFormat('yyyy-MM-dd'),
                reopenDate: DateTime.local().plus({ days: 1 }).toFormat('yyyy-MM-dd'),
            };
        }

        return {
            status: AppleBusinessConnectLocationStatus.OPEN,
        };
    }

    private _getCategories(restaurant: RestaurantPopulatedToPublish): string[] | undefined {
        if (!restaurant.category?.categoryId || !restaurant.categoryList || restaurant.categoryList.length === 0) {
            logger.warn('[Apple Business Connect] No categories found for restaurant');
            return undefined;
        }

        const mainAppleCategory = appleBusinessConnectCategoriesMapping[restaurant.category?.categoryId];
        let secondaryAppleCategories = restaurant.categoryList
            .map((category) => category.categoryId)
            .map((googleCategoryTag) => appleBusinessConnectCategoriesMapping[googleCategoryTag])
            .filter(isNotNil);

        if (mainAppleCategory) {
            // Filter out categories related to main category, included in it or including it
            secondaryAppleCategories = secondaryAppleCategories.filter(
                (category) => !(category.includes(mainAppleCategory) || mainAppleCategory.includes(category))
            );
        }

        // Remove redundancy
        const mostSpecificSecondaryCategories = this._getMostSpecificCategories(secondaryAppleCategories);

        // Main category should always be in first position
        const categories = [mainAppleCategory, ...mostSpecificSecondaryCategories].filter(isNotNil);

        if (categories.length === 0) {
            logger.warn('[Apple Business Connect] No categories for restaurant');
            return undefined;
        }

        return categories;
    }

    // Apple Business Connect doesn't like doublons, when a category is a prefix of another, we only keep the most specific one
    private _getMostSpecificCategories(categories: string[]): string[] {
        // Sort the array to get most specific categories first
        categories.sort((a, b) => b.split('.').length - a.split('.').length);

        // Initialize an empty array to store the most specific categories
        const mostSpecificCategories: string[] = [];

        // Iterate over the sorted array
        for (const category of categories) {
            const isAPrefix = mostSpecificCategories.some((cat) => cat.includes(category));

            // If the current category is not a prefix, add it to the result array
            if (!isAPrefix) {
                mostSpecificCategories.push(category);
            }
        }

        return mostSpecificCategories;
    }

    private _getOpeningHoursByDay(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationHoursByDay[] | undefined {
        const { regularHours } = restaurant;
        if (isNil(regularHours) || regularHours.length === 0) {
            logger.warn('[Apple Business Connect] No regular hours found for restaurant');
            return undefined;
        }

        const openingHours = regularHours.filter((regularHour) => !regularHour.isClosed);
        return openingHours.reduce<AppleBusinessConnectLocationHoursByDay[]>((openingHoursByDay, currentOpeningHours) => {
            if (!currentOpeningHours.openTime || !currentOpeningHours.closeTime) {
                return openingHoursByDay;
            }
            return this._addRegularHourToAbcHoursByDay(currentOpeningHours, openingHoursByDay);
        }, []);
    }

    private _getPhoneNumbers(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationPhoneNumber[] | undefined {
        const { phone } = restaurant;
        const phoneType = this._getAbcPhoneType(phone);

        if (!phone || !phone.digits || !phone.prefix || !phoneType) {
            logger.warn('[Apple Business Connect] No phone numbers found for restaurant');
            return undefined;
        }

        return [{ phoneNumber: `+${phone.prefix}${phone.digits}`, type: phoneType, primary: true }];
    }

    private _getAbcPhoneType(phone: RestaurantPopulatedToPublish['phone']): AppleBusinessConnectLocationPhoneNumberType | undefined {
        if (!phone || !phone.digits || !phone.prefix) {
            return undefined;
        }

        // TODO: check if the phone number is a mobile or a landline for all countries
        switch (phone.prefix) {
            case 33:
                return phone.digits.toString().match(/^(6|7)/)
                    ? AppleBusinessConnectLocationPhoneNumberType.MOBILE
                    : AppleBusinessConnectLocationPhoneNumberType.LANDLINE;
            default:
                return AppleBusinessConnectLocationPhoneNumberType.MOBILE;
        }
    }

    private async _getUrls(restaurant: RestaurantPopulatedToPublish): Promise<AppleBusinessConnectLocationUrl[] | undefined> {
        // Add platform URLs
        const urls = await this._platformsRepository.getUrlsByRestaurantId(restaurant._id.toString());
        const platformUrls = filterByRequiredKeys(
            urls
                .filter(({ url, platformKey }) => isNotNil(this._mapPlatformKey(platformKey)) && !!url && this._cleanUrl(url))
                .map(({ url, platformKey }) => ({ url: this._cleanUrl(url), type: this._mapPlatformKey(platformKey) })),
            ['url', 'type']
        );

        if (restaurant.website && this._cleanUrl(restaurant.website)) {
            platformUrls.push({ url: this._cleanUrl(restaurant.website), type: AppleBusinessConnectLocationUrlType.HOMEPAGE });
        }

        // Add social network URLs
        const handledSocialNetworkKeys = platformUrls.map(({ type }) => type);
        const socialNetworks = filterByRequiredKeys(
            restaurant.socialNetworkUrls
                ?.filter(
                    ({ url, key }) =>
                        this._mapSocialNetworkKey(key) &&
                        !handledSocialNetworkKeys.includes(this._mapSocialNetworkKey(key) as AppleBusinessConnectLocationUrlType) &&
                        !!url &&
                        this._cleanUrl(url)
                )
                .map(({ url, key }) => ({ url: this._cleanUrl(url), type: this._mapSocialNetworkKey(key) })) ?? [],
            ['type', 'url']
        );
        if (socialNetworks.length > 0) {
            platformUrls.push(...socialNetworks);
        }

        return platformUrls;
    }

    private _mapPlatformKey(platformKey: PlatformKey): AppleBusinessConnectLocationUrlType | undefined {
        return {
            [PlatformKey.FACEBOOK]: AppleBusinessConnectLocationUrlType.FACEBOOK,
            [PlatformKey.INSTAGRAM]: AppleBusinessConnectLocationUrlType.INSTAGRAM,
            [PlatformKey.YELP]: AppleBusinessConnectLocationUrlType.YELP,
        }[platformKey];
    }

    private _mapSocialNetworkKey(platformKey: SocialNetworkKey): AppleBusinessConnectLocationUrlType | undefined {
        return {
            [SocialNetworkKey.FACEBOOK]: AppleBusinessConnectLocationUrlType.FACEBOOK,
            [SocialNetworkKey.INSTAGRAM]: AppleBusinessConnectLocationUrlType.INSTAGRAM,
            [SocialNetworkKey.X]: AppleBusinessConnectLocationUrlType.TWITTER,
        }[platformKey];
    }

    private _cleanUrl(url: string): string {
        // clean utm parameters to prevent VALIDATION__UrlsUrlMustNotIncludeUtmParameter error
        const [urlWithoutParams, params] = url.split('?');
        let urlWithoutUtmParams: string;

        if (params) {
            const separatedParams = params.split('&');
            const notUtmParams: string[] = [];
            for (const param of separatedParams) {
                if (!param.startsWith('utm_')) {
                    notUtmParams.push(param);
                }
            }
            urlWithoutUtmParams = notUtmParams.length > 0 ? `${urlWithoutParams}?${notUtmParams.join('&')}` : urlWithoutParams;
        } else {
            urlWithoutUtmParams = urlWithoutParams;
        }

        // add http scheme if not already there to prevent VALIDATION__UrlsUrlMustHaveValidFormat error
        if (!urlWithoutUtmParams.startsWith('http')) {
            urlWithoutUtmParams = `http://${urlWithoutUtmParams}`;
        }

        return urlWithoutUtmParams;
    }

    private _getLocationAttributes(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationAttribute[] | undefined {
        const { attributeList } = restaurant;

        if (!attributeList || attributeList.length === 0) {
            logger.warn('[Apple Business Connect] No attributes found for restaurant for locationAttributes', {
                restaurantId: restaurant._id.toString(),
            });
            return undefined;
        }

        const attributeIds = attributeList
            .filter((attribute) => !!attribute.attribute?.attributeId && attribute.attributeValue === RestaurantAttributeValue.YES)
            .map((attribute) => attribute.attribute.attributeId);

        const appleBusinessConnectAttributes = uniq(
            attributeIds.map((attributeId) => appleBusinessConnectAttributesMapping[attributeId]).filter(isNotNil)
        );

        if (appleBusinessConnectAttributes.length === 0) {
            logger.warn('[Apple Business Connect] No attributes mapped for restaurant for locationAttributes', {
                restaurantId: restaurant._id.toString(),
                attributeIds,
            });
            return undefined;
        }

        return appleBusinessConnectAttributes.map((appleBusinessConnectAttribute) => ({
            name: appleBusinessConnectAttribute,
            value: 'true',
        }));
    }

    private _getLocationDescriptions(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationDescriptions[] | undefined {
        const { descriptions } = restaurant;
        if (!descriptions || descriptions.length === 0) {
            logger.warn('[Apple Business Connect] No descriptions found for restaurant');
            return undefined;
        }

        const descriptionsWithText = filterByRequiredKeys(descriptions, ['text', 'language']);
        if (descriptionsWithText.length === 0) {
            logger.warn('[Apple Business Connect] No computed descriptions for restaurant', {
                restaurantId: restaurant._id.toString(),
                descriptions,
            });

            return undefined;
        }

        const descriptionLanguages = uniq(descriptionsWithText.map((description) => description.language)).filter(isNotNil);
        if (descriptionLanguages.length === 0) {
            return undefined;
        }

        const locationDescriptions = descriptionLanguages.map((descriptionLanguage) =>
            this._getBestLocationDescriptionByLanguage(descriptionsWithText, descriptionLanguage)
        );

        return [{ type: 'ABOUT', descriptions: locationDescriptions }];
    }

    private _getBestLocationDescriptionByLanguage(
        descriptions: NonNullableKeys<RestaurantPopulatedToPublish['descriptions'][0], 'text' | 'language'>[],
        language: string
    ): AppleBusinessConnectLocationTextLocaleValue {
        const descriptionsByLanguage = descriptions.filter((description) => description.language === language);
        const shortDescription = descriptionsByLanguage.find((description) => description.size === DescriptionSize.SHORT);

        if (shortDescription) {
            return {
                text:
                    shortDescription.text.length <= this._APPLE_BUSINESS_CONNECT_MAX_DESCRIPTION_LENGTH
                        ? shortDescription.text
                        : shortDescription.text.substring(0, this._APPLE_BUSINESS_CONNECT_MAX_DESCRIPTION_LENGTH - 3) + '...',
                locale: this._mapLanguageToLocale(language),
            };
        }

        // Else, return first description
        return {
            text:
                descriptionsByLanguage[0].text.length <= this._APPLE_BUSINESS_CONNECT_MAX_DESCRIPTION_LENGTH
                    ? descriptionsByLanguage[0].text
                    : descriptionsByLanguage[0].text.substring(0, this._APPLE_BUSINESS_CONNECT_MAX_DESCRIPTION_LENGTH - 3) + '...',
            locale: this._mapLanguageToLocale(language),
        };
    }

    private async _getLocationKeywords(
        restaurant: RestaurantPopulatedToPublish
    ): Promise<AppleBusinessConnectLocationKeywords | undefined> {
        // TODO check after keyword refacto
        const keywords = (
            await this._keywordsRepository.find({
                filter: { restaurantId: restaurant._id },
                projection: { text: 1, language: 1, createdAt: 1 },
                options: { lean: true },
            })
        )
            .filter((keyword) => !!keyword.text && !!this._mapLanguageToLocale(keyword.language))
            .filter((keyword) => !keyword.text.includes('null') || !keyword.text.includes('undefined'))
            .sort((a, b) => new Date(b.createdAt).valueOf() - new Date(a.createdAt).valueOf())
            .slice(0, this._MAX_KEYWORDS_COUNT);

        if (keywords.length === 0) {
            logger.warn('[Apple Business Connect] No keywords found for restaurant');
            return undefined;
        }

        return {
            services: keywords.map(({ text, language }) => ({
                keyword: text,
                locale: this._mapLanguageToLocale(language),
            })),
        };
    }

    private _getPaymentMethods(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationPaymentMethods[] | undefined {
        const { attributeList } = restaurant;

        if (!attributeList || attributeList.length === 0) {
            logger.warn('[Apple Business Connect] No attributes found for restaurant for payment methods', {
                restaurantId: restaurant._id.toString(),
            });
            return undefined;
        }

        const attributeIds = attributeList
            .filter((attribute) => !!attribute?.attribute?.attributeId && attribute.attributeValue === RestaurantAttributeValue.YES)
            .map((attribute) => attribute.attribute.attributeId);

        const appleBusinessConnectPaymentMethods: AppleBusinessConnectLocationPaymentMethods[] = uniq(
            attributeIds
                .map((attributeId) => appleBusinessConnectPaymentMapping[attributeId])
                .filter(isNotNil)
                .map((attributeId) => {
                    // If there's a list
                    if (attributeId.includes(',')) {
                        return attributeId.split(',');
                    }

                    return attributeId;
                })
                .flat()
        );

        if (appleBusinessConnectPaymentMethods.length === 0) {
            logger.warn('[Apple Business Connect] No attributes mapped for restaurant for payment methods', {
                restaurantId: restaurant._id.toString(),
                attributeIds,
            });
            return undefined;
        }

        return appleBusinessConnectPaymentMethods;
    }

    private async _getServiceHours(
        restaurant: RestaurantPopulatedToPublish
    ): Promise<AppleBusinessConnectLocationServiceHours[] | undefined> {
        const { otherHours } = restaurant;

        if (!otherHours || otherHours.length === 0) {
            logger.warn('[Apple Business Connect] No other hours found for restaurant');
            return undefined;
        }

        const hoursTypes = await this._hourTypesRepository.find({
            filter: { _id: { $in: toDbIds(otherHours.map(({ hoursTypeId }) => hoursTypeId)) } },
            projection: { hoursType: 1 },
            options: { lean: true },
        });

        const serviceHours = otherHours
            .map(({ periods, hoursTypeId }) => {
                let hoursByDay: AppleBusinessConnectLocationHoursByDay[] = [];
                const openingPeriods = periods.filter((period) => !period.isClosed);

                openingPeriods.forEach((period) => {
                    hoursByDay = this._addRegularHourToAbcHoursByDay(period, hoursByDay);
                });
                if (hoursByDay.length === 0) {
                    return undefined;
                }

                const hoursType = hoursTypes.find((hourType) => hourType._id.toString() === hoursTypeId)?.hoursType;
                if (!hoursType) {
                    return undefined;
                }

                const category: string | undefined = appleBusinessConnectServiceHoursMapping[hoursType];
                if (!category) {
                    return undefined;
                }

                return { category, hoursByDay };
            })
            .filter(isNotNil);

        if (serviceHours.length === 0) {
            logger.warn('[Apple Business Connect] No service hours computed for restaurant', {
                restaurantId: restaurant._id.toString(),
                otherHours,
            });
            return undefined;
        }

        return serviceHours;
    }

    private _getSpecialHours(restaurant: RestaurantPopulatedToPublish): AppleBusinessConnectLocationSpecialHours[] | undefined {
        const { specialHours } = restaurant;

        if (!specialHours || specialHours.length === 0) {
            logger.warn('[Apple Business Connect] No special hours found for restaurant');
            return undefined;
        }

        const futureSpecialHours = filterByRequiredKeys(specialHours, ['startDate', 'endDate', 'openTime', 'closeTime']).filter(
            (specialHour) => isAfterToday(createDateFromMalouDate(specialHour.endDate))
        );

        const appleBusinessConnectSpecialHours = futureSpecialHours.map((specialHour) => {
            const startDate = createDateFromMalouDate(specialHour.startDate);
            const endDate = createDateFromMalouDate(specialHour.endDate);

            const translationsFr = this._translationService.fromLang({ lang: Locale.FR }).apple_business_connect;
            const translationsEn = this._translationService.fromLang({ lang: Locale.EN }).apple_business_connect;
            const translationsEs = this._translationService.fromLang({ lang: Locale.ES }).apple_business_connect;
            const translationsIt = this._translationService.fromLang({ lang: Locale.IT }).apple_business_connect;

            const descriptions = specialHour.isClosed
                ? [
                      { text: translationsFr.special_hours.closed(), locale: 'fr-FR' },
                      { text: translationsEn.special_hours.closed(), locale: 'en-US' },
                      { text: translationsEs.special_hours.closed(), locale: 'es-ES' },
                      { text: translationsIt.special_hours.closed(), locale: 'it-IT' },
                  ]
                : [
                      { text: translationsFr.special_hours.open(), locale: 'fr-FR' },
                      { text: translationsEn.special_hours.open(), locale: 'en-US' },
                      { text: translationsEs.special_hours.open(), locale: 'es-ES' },
                      { text: translationsIt.special_hours.open(), locale: 'it-IT' },
                  ];

            if (specialHour.isClosed) {
                // closed: true => do NOT include hoursByDay
                return {
                    startDate: DateTime.fromJSDate(startDate).toFormat('yyyy-MM-dd'),
                    endDate: DateTime.fromJSDate(endDate).toFormat('yyyy-MM-dd'),
                    closed: true,
                    descriptions,
                };
            }

            const hoursByDay = this._getHoursByDayFromSpecialHour(specialHour);
            return {
                startDate: DateTime.fromJSDate(startDate).toFormat('yyyy-MM-dd'),
                endDate: DateTime.fromJSDate(endDate).toFormat('yyyy-MM-dd'),
                closed: false,
                descriptions,
                hoursByDay,
            };
        });

        if (appleBusinessConnectSpecialHours.length === 0) {
            logger.warn('[Apple Business Connect] No special hours computed for restaurant', {
                restaurantId: restaurant._id.toString(),
                specialHours,
            });
            return undefined;
        }

        return appleBusinessConnectSpecialHours as AppleBusinessConnectLocationSpecialHours[];
    }

    private _mapDayToAppleBusinessConnectDay(day: Day): AppleBusinessConnectLocationDay {
        switch (day) {
            case Day.MONDAY:
                return AppleBusinessConnectLocationDay.MONDAY;
            case Day.TUESDAY:
                return AppleBusinessConnectLocationDay.TUESDAY;
            case Day.WEDNESDAY:
                return AppleBusinessConnectLocationDay.WEDNESDAY;
            case Day.THURSDAY:
                return AppleBusinessConnectLocationDay.THURSDAY;
            case Day.FRIDAY:
                return AppleBusinessConnectLocationDay.FRIDAY;
            case Day.SATURDAY:
                return AppleBusinessConnectLocationDay.SATURDAY;
            case Day.SUNDAY:
                return AppleBusinessConnectLocationDay.SUNDAY;
        }
    }

    private _mapLanguageToLocale(lang: string): string {
        switch (lang) {
            case 'fr':
                return 'fr-FR';
            case 'en':
                return 'en-US';
            case 'it':
                return 'it-IT';
            case 'es':
                return 'es-ES';
            default:
                return 'en-US';
        }
    }

    private _addRegularHourToAbcHoursByDay(
        regularHour: NonNullable<RestaurantPopulatedToPublish['regularHours']>[0],
        hoursByDay: AppleBusinessConnectLocationHoursByDay[]
    ): AppleBusinessConnectLocationHoursByDay[] {
        const times = { startTime: regularHour.openTime ?? '00:00', endTime: regularHour.closeTime ?? '24:00' };
        const abcDay = this._mapDayToAppleBusinessConnectDay(regularHour.openDay);

        const existingHoursByDay = hoursByDay.find((hours) => hours.day === abcDay);
        if (existingHoursByDay) {
            existingHoursByDay.times.push(times);
        } else {
            hoursByDay.push({ day: abcDay, times: [times] });
        }

        return hoursByDay;
    }

    private _getHoursByDayFromSpecialHour(
        specialHour: NonNullableKeys<RestaurantPopulatedToPublish['specialHours'][0], 'startDate' | 'endDate' | 'openTime' | 'closeTime'>
    ): AppleBusinessConnectLocationHoursByDay[] {
        const startDate = DateTime.fromJSDate(createDateFromMalouDate(specialHour.startDate));
        const endDate = specialHour.endDate ? DateTime.fromJSDate(createDateFromMalouDate(specialHour.endDate)) : undefined;

        const times = [{ startTime: specialHour.openTime, endTime: specialHour.closeTime }];

        const weekdays = Object.values(AppleBusinessConnectLocationDay);

        if (!endDate) {
            return weekdays.map((day) => ({ day, times }));
        }

        const startWeekday = startDate.weekday; // day of the week, 1 is Monday and 7 is Sunday
        const diffBetweenStartAndEnd = Math.min(startDate.diff(endDate, 'days').toObject().days ?? 0, 6);

        let i = 0;
        const hoursByDay: AppleBusinessConnectLocationHoursByDay[] = [];

        while (i <= diffBetweenStartAndEnd) {
            const dayIndex = (startWeekday + i - 1) % 7;
            hoursByDay.push({ day: weekdays[dayIndex], times });
            i++;
        }

        return hoursByDay;
    }
}
