import { container } from 'tsyringe';

import { ApplicationLanguage, TranslationSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { Keyword, KeywordProps } from ':modules/keywords/entities/keyword.entity';
import { TranslationsWithoutId } from ':modules/keywords/services/keyword-translation-service/keyword-translation.interface';
import { KeywordTranslationPort } from ':modules/keywords/services/keyword-translation-service/keyword-translation.port';
import { CreateKeywordsUseCase } from ':modules/keywords/use-cases/create-keywords/create-keywords.use-case';
import { Translations } from ':modules/translations/entities/translations.entity';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';

describe('CreateKeywordsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['TranslationsRepository', 'KeywordsTempRepository']);
    });

    describe('execute', () => {
        class ExperimentationServiceMock {
            isFeatureAvailable = jest.fn().mockResolvedValue(true);
        }
        container.register(ExperimentationService, { useValue: new ExperimentationServiceMock() as unknown as ExperimentationService });

        class KeywordTranslationPortMock {
            getTranslations(data: { texts: string[]; language: ApplicationLanguage }[]): Promise<TranslationsWithoutId[]> {
                return Promise.resolve(
                    data
                        .map(({ texts, language }) =>
                            texts.map((text) => ({
                                fr: text,
                                en: text,
                                es: text,
                                it: text,
                                language,
                                source: TranslationSource.SERVERLESS_TEXT_TRANSLATOR,
                            }))
                        )
                        .flat()
                );
            }
        }
        container.register(KeywordTranslationPort, { useValue: new KeywordTranslationPortMock() as any });

        it('should translate breakdowns', async () => {
            const createKeywordsUseCase = container.resolve(CreateKeywordsUseCase);

            const keywords: Omit<KeywordProps, 'id' | 'createdAt' | 'updatedAt'>[] = [
                {
                    apiLocationId: '1',
                    text: 'keyword1',
                    bricks: [],
                    language: ApplicationLanguage.EN,
                    isCustomerInput: false,
                    volume: 0,
                    volumeHistory: [],
                    volumeFromAdmin: undefined,
                    lastVolumeFetchDate: null,
                },
                {
                    apiLocationId: '1',
                    text: 'keyword2',
                    bricks: [new Breakdown({ text: 'breakdowntext', category: 'venueAttribute' })],
                    language: ApplicationLanguage.FR,
                    isCustomerInput: false,
                    volume: 0,
                    volumeHistory: [],
                    volumeFromAdmin: undefined,
                    lastVolumeFetchDate: null,
                },
            ];

            const testCase = new TestCaseBuilderV2<'keywordsTemp' | 'translations'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [];
                        },
                    },
                    translations: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult(): Keyword[] {
                    return [
                        new Keyword({
                            ...keywords[0],
                            id: expect.any(String),
                            createdAt: expect.any(Date),
                            updatedAt: expect.any(Date),
                        }),
                        new Keyword({
                            ...keywords[1],
                            id: expect.any(String),
                            bricks: keywords[1].bricks.map(
                                (brick) =>
                                    new Breakdown({
                                        ...brick,
                                        translations: new Translations({
                                            id: expect.any(String),
                                            fr: brick.text,
                                            en: brick.text,
                                            es: brick.text,
                                            it: brick.text,
                                            language: keywords[1].language as ApplicationLanguage,
                                            source: expect.any(String),
                                        }),
                                        translationsId: expect.any(String),
                                    })
                            ),
                            createdAt: expect.any(Date),
                            updatedAt: expect.any(Date),
                        }),
                    ];
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await createKeywordsUseCase.execute(keywords);

            expect(result).toIncludeSameMembers(expectedResult);
        });
    });
});
