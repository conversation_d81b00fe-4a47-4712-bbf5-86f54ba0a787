import { chunk, from<PERSON><PERSON><PERSON>, groupBy, to<PERSON><PERSON>s, uniq, uniqWith } from 'lodash';
import { singleton } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    isFulfilled,
    isValidKeywordText,
    mapLanguageStringToApplicationLanguage,
    WatcherStatus,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GeneratedKeywords, KeywordsGeneratorResponse } from ':microservices/keywords-generator';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { KeywordsGeneratorAiInteractionEntity } from ':modules/ai-interactions/entities/keywords-generator-ai-interaction.entity';
import { BreakdownAndClassifyKeywordsUseCase } from ':modules/ai/use-cases';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { Keyword, KeywordProps } from ':modules/keywords/entities/keyword.entity';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import { KeywordVolumeWithHistory } from ':modules/keywords/services/keyword-volume-service/keyword-volume.interfaces';
import { KeywordsVolumePort } from ':modules/keywords/services/keyword-volume-service/keyword-volume.port';
import { KeywordsGenerationStatusService } from ':modules/keywords/services/keywords-generation-status.service';
import { CreateKeywordsUseCase } from ':modules/keywords/use-cases/create-keywords/create-keywords.use-case';
import { FetchKeywordVolumesUseCase } from ':modules/keywords/use-cases/fetch-keyword-volumes/fetch-keyword-volumes.use-case';
import { RestaurantKeyword } from ':modules/restaurant-keywords/entities/restaurant-keywords.entity';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';

type GeneratedKeywordWithBricks = {
    text: string;
    language: string;
    bricks: {
        text: string;
        category: string;
    }[];
};

@singleton()
export class ProcessGeneratedKeywordsUseCase {
    readonly MIN_KEYWORDS_CHUNK_TO_BREAKDOWN = 10;

    constructor(
        private readonly _keywordsRepository: KeywordsTempRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _keywordsVolumePort: KeywordsVolumePort,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _createKeywordsUseCase: CreateKeywordsUseCase,
        private readonly _breakdownAndClassifyKeywordsUseCase: BreakdownAndClassifyKeywordsUseCase,
        private readonly _fetchKeywordVolumesUseCase: FetchKeywordVolumesUseCase,
        private readonly _keywordsGenerationStatusService: KeywordsGenerationStatusService
    ) {}

    async execute(generatorResponse: { data: KeywordsGeneratorResponse }): Promise<void> {
        const {
            keywords,
            aiInteractions,
            from: { restaurantId, userId, apiLocationId },
        } = generatorResponse.data;
        try {
            const sampledKeywords = this._getSampledKeywords(keywords, restaurantId);
            const { keywordsWithBricks, keywordsWithoutBricks } = this._separateAndFormatGeneratedKeywords(sampledKeywords);

            await this._saveAiInteractions(restaurantId, userId, aiInteractions);

            const breakdownKeywords = await this._breakdownKeywords(keywordsWithoutBricks, restaurantId, userId);
            const generatedKeywords = [...keywordsWithBricks, ...breakdownKeywords];

            const upsertedKeywords = await this._upsertGeneratedKeywords(generatedKeywords, apiLocationId);

            const { newKeywords, oldKeywordIds } = await this._removePreviousKeywordsFromRestaurant(restaurantId, upsertedKeywords);
            await this._createRestaurantKeywords(restaurantId, newKeywords);

            await this._keywordsRepository.deleteKeywordsWithoutAttachedRestaurants(oldKeywordIds);
            logger.info('[ProcessGeneratedKeywordsUseCase] - setStatus finished for restaurantId', { restaurantId });
            await this._keywordsGenerationStatusService.setStatus(restaurantId, { status: WatcherStatus.FINISHED });
        } catch (error: any) {
            logger.error(`Error processing generated keywords ${restaurantId}`, error);
            await this._keywordsGenerationStatusService.setStatus(restaurantId, {
                status: WatcherStatus.FAILED,
                error: error.message ?? JSON.stringify(error),
            });
        }
    }

    private async _upsertGeneratedKeywords(generatedKeywords: GeneratedKeywordWithBricks[], apiLocationId: string): Promise<Keyword[]> {
        generatedKeywords = uniqWith(generatedKeywords, (a, b) => a.text === b.text && a.language === b.language);
        const generatedKeywordsWithoutBricks = generatedKeywords.map((keyword) => ({
            apiLocationId: apiLocationId,
            text: keyword.text,
            language: keyword.language,
        }));
        const existingKeywords = await this._keywordsRepository.findKeywords(generatedKeywordsWithoutBricks);

        const generatedKeywordsToCreate = generatedKeywords.filter(
            (keyword) =>
                !existingKeywords.some(
                    (existingKeyword) =>
                        existingKeyword.apiLocationId === apiLocationId &&
                        existingKeyword.text === keyword.text &&
                        existingKeyword.language === keyword.language
                )
        );

        const generatedKeywordsToCreateTexts = uniq(generatedKeywordsToCreate.map((keyword) => keyword.text));
        const keywordVolumesWithHistory = await this._getKeywordsVolumeWithHistory(generatedKeywordsToCreateTexts, apiLocationId);
        const now = new Date();

        const keywordsToCreate: Omit<KeywordProps, 'id' | 'createdAt' | 'updatedAt'>[] = generatedKeywordsToCreate.map((keyword) => {
            const keywordVolumeFound = keywordVolumesWithHistory?.find((keywordVolume) => keywordVolume.text === keyword.text);
            const volume = keywordVolumeFound?.volume ?? null;
            const volumeHistory = keywordVolumeFound?.volumeHistory ?? [];
            const lastVolumeFetchDate = keywordVolumeFound ? now : null;

            return {
                ...keyword,
                bricks: keyword.bricks.map(
                    (brick) =>
                        new Breakdown({
                            ...brick,
                            translations: undefined,
                        })
                ),
                isCustomerInput: false,
                volume,
                volumeHistory,
                apiLocationId: apiLocationId,
                lastVolumeFetchDate,
            };
        });
        const keywordsToCreateFiltered = keywordsToCreate.filter((keyword) => keyword.volume !== 0);

        const createdKeywords = await this._createKeywordsUseCase.execute(keywordsToCreateFiltered);

        return [...existingKeywords, ...createdKeywords];
    }

    private async _removePreviousKeywordsFromRestaurant(
        restaurantId: string,
        keywords: Keyword[]
    ): Promise<{ newKeywords: Keyword[]; oldKeywordIds: string[] }> {
        const keywordIds = keywords.map((keyword) => keyword.id);
        const oldKeywordIds = await this._restaurantKeywordsRepository.removePreviousRestaurantKeywords(restaurantId, keywordIds);
        return { newKeywords: keywords.filter((keyword) => !oldKeywordIds.includes(keyword.id)), oldKeywordIds };
    }

    private async _createRestaurantKeywords(restaurantId: string, keywords: Keyword[]): Promise<RestaurantKeyword[]> {
        const restaurantKeywords: RestaurantKeyword[] = keywords.map(
            (keyword) =>
                new RestaurantKeyword({
                    id: newDbId().toString(),
                    restaurantId,
                    keywordId: keyword.id,
                    selected: false,
                    duplicatedFromRestaurantId: undefined,
                    keyword: keyword,
                })
        );

        return this._restaurantKeywordsRepository.createManyRestaurantKeywords(restaurantKeywords);
    }

    private async _saveAiInteractions(
        restaurantId: string,
        userId: string,
        aiInteractions: KeywordsGeneratorResponse['aiInteractions']
    ): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map(
            (aiInteraction) => new KeywordsGeneratorAiInteractionEntity(aiInteraction, restaurantId, userId)
        );
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }

    private _separateAndFormatGeneratedKeywords(data: KeywordsGeneratorResponse['keywords']): {
        keywordsWithBricks: GeneratedKeywordWithBricks[];
        keywordsWithoutBricks: Omit<GeneratedKeywordWithBricks, 'bricks'>[];
    } {
        const keywordsWithBricks: GeneratedKeywordWithBricks[] = [];
        const keywordsWithoutBricks: Omit<GeneratedKeywordWithBricks, 'bricks'>[] = [];

        for (const lang in data) {
            if (Object.values(ApplicationLanguage).includes(mapLanguageStringToApplicationLanguage(lang))) {
                const langKeywords: GeneratedKeywords = data[lang];
                const langKeywordsWithBricks = (langKeywords.keywordsWithBricks ?? []).map((keywordWithBricks) => ({
                    text: keywordWithBricks.text,
                    bricks: keywordWithBricks.bricks,
                    language: lang,
                }));
                const keywordsWithBricksTexts = langKeywordsWithBricks.map((keyword) => keyword.text);
                const langKeywordsWithoutBricks = (langKeywords.generatedKeywords ?? [])
                    .filter((keyword) => !keywordsWithBricksTexts.includes(keyword))
                    .map((keyword) => ({ text: keyword, language: lang }));

                keywordsWithBricks.push(...langKeywordsWithBricks);
                keywordsWithoutBricks.push(...langKeywordsWithoutBricks);
            }
        }

        const uniqueKeywordsWithBricks = uniqWith(keywordsWithBricks, (a, b) => a.text === b.text && a.language === b.language);
        const uniqueKeywordsWithoutBricks = uniqWith(keywordsWithoutBricks, (a, b) => a.text === b.text && a.language === b.language);

        const cleanUniqueKeywordsWithBricks = uniqueKeywordsWithBricks.filter((keyword) => isValidKeywordText(keyword.text ?? ''));
        const cleanUniqueKeywordsWithoutBricks = uniqueKeywordsWithoutBricks.filter((keyword) => isValidKeywordText(keyword.text ?? ''));

        return { keywordsWithBricks: cleanUniqueKeywordsWithBricks, keywordsWithoutBricks: cleanUniqueKeywordsWithoutBricks };
    }

    private async _breakdownKeywords(
        keywords: Omit<GeneratedKeywordWithBricks, 'bricks'>[],
        restaurantId: string,
        userId: string
    ): Promise<GeneratedKeywordWithBricks[]> {
        const existingKeywords = await this._keywordsRepository.findKeywords(keywords);
        const keywordsToBreakdown = keywords.filter(
            (keyword) =>
                !existingKeywords.some(
                    (existingKeyword) => existingKeyword.text === keyword.text && existingKeyword.language === keyword.language
                )
        );

        const keywordsGroupedByLang = groupBy(keywordsToBreakdown, 'language');

        const promises = Object.entries(keywordsGroupedByLang).map(([lang, langKeywords]) => {
            const keywordTexts = langKeywords.map((keyword) => keyword.text);
            const uniqueKeywordTexts = Array.from(new Set(keywordTexts));
            const keywordChunks = chunk(uniqueKeywordTexts, this.MIN_KEYWORDS_CHUNK_TO_BREAKDOWN);
            return keywordChunks.map(async (keywordTextsChunk) => {
                return this._breakdownAndClassifyKeywordsUseCase
                    .execute(keywordTextsChunk, restaurantId, lang, userId)
                    .then((breakdownResults) => breakdownResults.map((breakdownKeyword) => ({ ...breakdownKeyword, language: lang })));
            });
        });

        const breakdownResults = await Promise.allSettled(promises.flat());
        const breakdownResultsFlattened = breakdownResults
            .filter(isFulfilled)
            .map((result) => result.value)
            .flat();
        const formattedBreakdownResults = breakdownResultsFlattened.map((kw) => ({
            text: kw.keyword,
            language: kw.language,
            bricks: kw.bricks?.map((b) => ({ text: b.text, category: b.category })) ?? [],
        }));

        const breakdownKeywords = formattedBreakdownResults
            .concat(existingKeywords.map((keyword) => ({ text: keyword.text, language: keyword.language, bricks: keyword.bricks })))
            .filter((keyword) => keyword.text?.length > 0);

        return breakdownKeywords;
    }

    private async _getKeywordsVolumeWithHistory(keywords: string[], apiLocationId: string): Promise<KeywordVolumeWithHistory[] | null> {
        let isKeywordProviderQuotaReached = true;
        try {
            isKeywordProviderQuotaReached = await this._keywordsVolumePort.isKeywordProviderQuotaReached();
        } catch (error) {
            logger.error(`[ProcessGeneratedKeywordsUseCase] - Error getting keyword provider quota remaining`, error);
        }
        if (isKeywordProviderQuotaReached) {
            return null;
        }
        return this._fetchKeywordVolumesUseCase.execute(keywords, apiLocationId);
    }

    private _getSampledKeywords(
        keywords: KeywordsGeneratorResponse['keywords'],
        restaurantId: string
    ): KeywordsGeneratorResponse['keywords'] {
        // For testing purposes, we might limit the number of keywords to 5 to accelerate the tests
        const testRestauranId = '66bb2ea38f8db6a6abf99ef2'; // le Nino Traiteur e2e dev
        if (restaurantId !== testRestauranId) {
            return keywords;
        }
        return fromPairs(
            toPairs(keywords).map(([locale, generatedKeywords]) => {
                if (!generatedKeywords) return [locale, generatedKeywords];
                return [
                    locale,
                    {
                        generatedKeywords: generatedKeywords.generatedKeywords.slice(0, 5),
                        keywordsWithBricks: generatedKeywords.keywordsWithBricks.slice(0, 5),
                    },
                ];
            })
        );
    }
}
