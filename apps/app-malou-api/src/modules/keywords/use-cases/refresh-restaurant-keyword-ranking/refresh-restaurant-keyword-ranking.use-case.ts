import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GeoSampleDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { BusinessCategory, CcTld, ccTldByCountryCode, GMapsApiVersion, ILatlng, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { KeywordsDtoMapper } from ':modules/keywords/keywords.mapper.dto';
import { GeoSampleService } from ':modules/keywords/services/geo-sample.service';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class RefreshRestaurantKeywordRankingUseCase {
    constructor(
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _geoSampleService: GeoSampleService,
        private readonly _keywordsDtoMapper: KeywordsDtoMapper
    ) {}

    async execute(restaurantKeywordId: string): Promise<GeoSampleDto[]> {
        const restaurantKeyword = await this._restaurantKeywordsRepository.findById(restaurantKeywordId);

        if (!restaurantKeyword.canBeRefreshed()) {
            throw new MalouError(MalouErrorCode.RESTAURANT_KEYWORD_RANKING_REFRESH_NOT_ALLOWED, {
                message: 'Restaurant keyword ranking was refreshed less than 24 hours ago',
                metadata: { lastRankingRefresh: restaurantKeyword.lastRankingRefresh },
            });
        }

        const restaurant = await this._restaurantsRepository.findOne({
            filter: {
                _id: toDbId(restaurantKeyword.restaurantId),
            },
            projection: { gMapsApiVersion: 1 },
            options: { lean: true },
        });

        const gMapsApiVersion = restaurant?.gMapsApiVersion ?? GMapsApiVersion.V2;

        const { latlng, region } = await this._getLatLngAndRegionForRestaurantKeyword(restaurantKeyword.restaurantId);
        const samplesToFetch = this._geoSampleService.generateWantedSearchQueries({
            keywordsWithRegion: [{ keyword: restaurantKeyword.keyword.text, region }],
            latlngs: [latlng],
            gMapsApiVersion,
        });

        const ranks = await this._geoSampleService.fetchKeywordsForLatsLngs(samplesToFetch);
        await this._geoSampleService.upsertGeosamples(ranks);
        await this._restaurantKeywordsRepository.updateById(restaurantKeywordId, { lastRankingRefresh: new Date() });
        const now = DateTime.now();
        const todaysWeek = now.weekNumber;
        const todaysYear = now.weekYear;
        const result = await this._geoSampleService.getRectangleGeoSamplesForLatAndLngAndKeywords({
            latlngs: [latlng],
            keywords: [restaurantKeyword.keyword.text],
            weeks: [todaysWeek],
            years: [todaysYear],
        });
        return result.map(this._keywordsDtoMapper.toGeoSampleDto);
    }

    /** The restaurant must have the type BusinessCategory.LOCAL_BUSINESS (not BRAND) */
    private async _getLatLngAndRegionForRestaurantKeyword(restaurantId: string): Promise<{ latlng: ILatlng; region: CcTld }> {
        const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'Restaurant not found',
                metadata: { id: restaurantId },
            });
        }

        const latlng = restaurant.latlng;
        assert.equal(restaurant.type, BusinessCategory.LOCAL_BUSINESS);
        assert(restaurant.address, 'a restaurant with the type BusinessCategory.LOCAL_BUSINESS must have an address');
        const regionCode = restaurant.address.regionCode;
        const region = ccTldByCountryCode[regionCode];

        if (!latlng || !latlng.lat || !latlng.lng) {
            throw new MalouError(MalouErrorCode.RESTAURANT_MISSING_LATLNG, {
                message: 'Restaurant missing latlng',
                metadata: { id: restaurantId },
            });
        }
        assert(region, 'Region not found');

        return { latlng, region };
    }
}
