import { EntityConstructor, KeywordVolumeProvider } from '@malou-io/package-utils';

import { VolumeHistory } from ':modules/keywords/entities/volume-history.entity';

export interface KeywordVolumeWithHistory {
    text: string;
    volume: number;
    volumeHistory: EntityConstructor<VolumeHistory>[];
}

export interface IKeywordVolume {
    data: {
        text: string;
        volume: number;
    }[];
    source: KeywordVolumeProvider;
}

export interface IKeywordVolumePort {
    getKeywordsVolume(keywords: string[], apiLocationId: string): Promise<IKeywordVolume>;
    getKeywordsVolumeWithHistory(keywords: string[], apiLocationId: string): Promise<KeywordVolumeWithHistory[]>;
    isKeywordProviderQuotaReached(): Promise<boolean>;
    isMinuteKeywordProviderQuotaReached(): Promise<boolean>;
    isDailyKeywordProviderQuotaReached(): Promise<boolean>;
    isMonthlyKeywordProviderQuotaReached(): Promise<boolean>;
    getDailyKeywordToolQuotaRemaining(): Promise<number>;
    getMaxKeywordsPerRequest(): number;
    getMaxApiCallsPerMinute(): number;
    getVolumePortSource(): KeywordVolumeProvider;
}
