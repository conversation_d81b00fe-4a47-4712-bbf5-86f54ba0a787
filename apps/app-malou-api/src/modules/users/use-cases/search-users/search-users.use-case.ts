import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { OrganizationUserDto, SearchUserDto, SearchUsersQueryDto } from '@malou-io/package-dto';
import { ID, IUser } from '@malou-io/package-models';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class SearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: SearchUsersQueryDto): Promise<{ data: Partial<OrganizationUserDto>[]; total: number }> {
        const { text, fields = [], limit, offset } = params;

        // If no search text, use simple find
        if (!text?.trim()) {
            const filter: any = {};
            const options: any = {
                populate: [
                    {
                        path: 'organizations',
                    },
                    {
                        path: 'profilePicture',
                    },
                ],
                lean: true,
                sort: {
                    createdAt: -1,
                    email: 1,
                },
                readPreference: ReadPreference.SECONDARY_PREFERRED,
            };

            if (limit !== undefined) {
                options.limit = limit;
            }
            if (offset !== undefined) {
                options.skip = offset;
            }

            const projection =
                fields.length > 0
                    ? fields.reduce((acc, field) => ({ ...acc, [field]: 1 }), { _id: 1 })
                    : {
                          _id: 1,
                          email: 1,
                          name: 1,
                          lastname: 1,
                          role: 1,
                          organizationIds: 1,
                          defaultLanguage: 1,
                          createdAt: 1,
                          updatedAt: 1,
                          verified: 1,
                      };

            const [users, totalCount] = await Promise.all([
                this._usersRepository.find({
                    filter,
                    projection,
                    options,
                }),
                this._usersRepository.countDocuments({ filter }),
            ]);

            return {
                data: users.map((u) => this._toDto(u)),
                total: totalCount,
            };
        }

        // Use aggregation pipeline for search with organization names
        const searchText = text.trim();
        const searchRegex = toDiacriticInsensitiveRegexString(searchText);

        const pipeline: any[] = [
            // Lookup organizations
            {
                $lookup: {
                    from: 'organizations',
                    localField: 'organizationIds',
                    foreignField: '_id',
                    as: 'organizations',
                },
            },
            // Match users by email or organization name
            {
                $match: {
                    $or: [
                        { email: { $regex: searchRegex, $options: 'i' } },
                        { name: { $regex: searchRegex, $options: 'i' } },
                        { lastname: { $regex: searchRegex, $options: 'i' } },
                        { 'organizations.name': { $regex: searchRegex, $options: 'i' } },
                    ],
                },
            },
            // Sort
            {
                $sort: {
                    createdAt: -1,
                    email: 1,
                },
            },
        ];

        // Add projection if fields are specified
        if (fields.length > 0) {
            const projection = fields.reduce((acc, field) => ({ ...acc, [field]: 1 }), { _id: 1 });
            pipeline.push({ $project: projection });
        } else {
            pipeline.push({
                $project: {
                    _id: 1,
                    email: 1,
                    name: 1,
                    lastname: 1,
                    role: 1,
                    organizationIds: 1,
                    defaultLanguage: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    verified: 1,
                    organizations: 1,
                },
            });
        }

        // Count pipeline for total
        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        // Add pagination to main pipeline
        if (offset !== undefined) {
            pipeline.push({ $skip: offset });
        }
        if (limit !== undefined) {
            pipeline.push({ $limit: limit });
        }

        const [users, countResult] = await Promise.all([
            this._usersRepository.aggregate(pipeline),
            this._usersRepository.aggregate(countPipeline),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: users.map((u) => this._toDto(u)),
            total,
        };
    }

    private _toDto(user: Partial<IUser> & { _id: ID }): SearchUserDto {
        return {
            _id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
            email: user.email,
            role: user.role,
            defaultLanguage: user.defaultLanguage,
        };
    }
}
