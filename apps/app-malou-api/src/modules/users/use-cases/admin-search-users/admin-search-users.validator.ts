import { validateSync } from 'class-validator';

import { AdminSearchUsersQueryDto } from './admin-search-users.dto';

export const adminSearchUsersQueryValidator = (req: any, res: any, next: any) => {
    const dto = Object.assign(new AdminSearchUsersQueryDto(), req.query);
    const errors = validateSync(dto);

    if (errors.length > 0) {
        return res.status(400).json({
            message: 'Validation failed',
            errors: errors.map((error) => ({
                property: error.property,
                constraints: error.constraints,
            })),
        });
    }

    req.query = dto;
    next();
};
