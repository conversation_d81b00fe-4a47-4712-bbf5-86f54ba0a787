import { AdminSearchUsersUseCase } from './admin-search-users.use-case';
import { UsersRepository } from '../../users.repository';

describe('AdminSearchUsersUseCase', () => {
    let useCase: AdminSearchUsersUseCase;
    let usersRepository: jest.Mocked<UsersRepository>;

    beforeEach(() => {
        usersRepository = {
            aggregate: jest.fn(),
        } as any;

        useCase = new AdminSearchUsersUseCase(usersRepository);
    });

    describe('execute', () => {
        it('should return users with pagination', async () => {
            const mockUsers = [
                { _id: '1', name: '<PERSON>', email: '<EMAIL>' },
                { _id: '2', name: '<PERSON>', email: '<EMAIL>' },
            ];

            usersRepository.aggregate.mockResolvedValueOnce([
                { data: mockUsers, total: [{ count: 2 }] },
            ]);

            const result = await useCase.execute({
                text: 'john',
                limit: 10,
                offset: 0,
            });

            expect(result).toEqual({
                data: mockUsers,
                total: 2,
            });

            expect(usersRepository.aggregate).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        $lookup: expect.objectContaining({
                            from: 'organizations',
                        }),
                    }),
                    expect.objectContaining({
                        $lookup: expect.objectContaining({
                            from: 'restaurants',
                        }),
                    }),
                    expect.objectContaining({
                        $match: expect.objectContaining({
                            $or: expect.any(Array),
                        }),
                    }),
                ])
            );
        });

        it('should handle empty search text', async () => {
            const mockUsers = [
                { _id: '1', name: 'John', email: '<EMAIL>' },
            ];

            usersRepository.aggregate.mockResolvedValueOnce([
                { data: mockUsers, total: [{ count: 1 }] },
            ]);

            const result = await useCase.execute({
                limit: 10,
                offset: 0,
            });

            expect(result).toEqual({
                data: mockUsers,
                total: 1,
            });
        });

        it('should apply default pagination values', async () => {
            usersRepository.aggregate.mockResolvedValueOnce([
                { data: [], total: [{ count: 0 }] },
            ]);

            await useCase.execute({});

            expect(usersRepository.aggregate).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ $skip: 0 }),
                    expect.objectContaining({ $limit: 20 }),
                ])
            );
        });
    });
});
