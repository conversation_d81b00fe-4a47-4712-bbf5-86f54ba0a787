import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { ID, IUser } from '@malou-io/package-models';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { UsersRepository } from ':modules/users/users.repository';

export interface AdminSearchUsersQueryDto {
    text?: string;
    limit?: number;
    offset?: number;
}

export interface AdminSearchUsersResponseDto {
    data: IUser[];
    total: number;
}

@singleton()
export class AdminSearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: AdminSearchUsersQueryDto): Promise<AdminSearchUsersResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        // Build aggregation pipeline
        const pipeline: any[] = [];

        // Lookup organizations to enable search by organization name
        pipeline.push({
            $lookup: {
                from: 'organizations',
                localField: 'organizationIds',
                foreignField: '_id',
                as: 'organizations',
            },
        });

        // Lookup restaurants to get full restaurant data
        pipeline.push({
            $lookup: {
                from: 'restaurants',
                localField: '_id',
                foreignField: 'userIds',
                as: 'restaurants',
            },
        });

        // Add search filter if text is provided
        if (text?.trim()) {
            const searchRegex = toDiacriticInsensitiveRegexString(text.trim());
            
            pipeline.push({
                $match: {
                    $or: [
                        { email: { $regex: searchRegex, $options: 'i' } },
                        { name: { $regex: searchRegex, $options: 'i' } },
                        { lastname: { $regex: searchRegex, $options: 'i' } },
                        { 'organizations.name': { $regex: searchRegex, $options: 'i' } },
                    ],
                },
            });
        }

        // Sort by creation date (newest first) and email
        pipeline.push({
            $sort: {
                createdAt: -1,
                email: 1,
            },
        });

        // Create count pipeline before adding pagination
        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        // Add pagination to main pipeline
        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        // Execute both pipelines in parallel
        const [users, countResult] = await Promise.all([
            this._usersRepository.aggregate(pipeline, { readPreference: ReadPreference.SECONDARY_PREFERRED }),
            this._usersRepository.aggregate(countPipeline, { readPreference: ReadPreference.SECONDARY_PREFERRED }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: users,
            total,
        };
    }
}
