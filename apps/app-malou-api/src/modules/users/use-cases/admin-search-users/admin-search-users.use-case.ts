import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { IUser } from '@malou-io/package-models';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { UsersRepository } from ':modules/users/users.repository';

export interface AdminSearchUsersParams {
    text?: string;
    limit?: number;
    offset?: number;
}

export interface AdminSearchUsersResponseDto {
    data: IUser[];
    total: number;
}

@singleton()
export class AdminSearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: AdminSearchUsersParams): Promise<AdminSearchUsersResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        // Build aggregation pipeline
        const pipeline: any[] = [];

        // Lookup organizations to enable search by organization name
        pipeline.push({
            $lookup: {
                from: 'organizations',
                localField: 'organizationIds',
                foreignField: '_id',
                as: 'organizations',
            },
        });

        // Lookup restaurants through UserRestaurants collection
        pipeline.push({
            $lookup: {
                from: 'userrestaurants',
                localField: '_id',
                foreignField: 'userId',
                as: 'restaurants',
                pipeline: [
                    {
                        $lookup: {
                            from: 'restaurants',
                            localField: 'restaurantId',
                            foreignField: '_id',
                            as: 'restaurantDetails',
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        active: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: { 'restaurantDetails.active': true },
                    },
                    {
                        // Unwind restaurantDetails array, as there is only one element
                        $addFields: {
                            restaurantDetails: { $arrayElemAt: ['$restaurantDetails', 0] },
                        },
                    },
                ],
            },
        });

        // Add search filter if text is provided
        if (text?.trim()) {
            const searchRegex = toDiacriticInsensitiveRegexString(text.trim());

            pipeline.push({
                $match: {
                    $or: [
                        { email: { $regex: searchRegex, $options: 'i' } },
                        { name: { $regex: searchRegex, $options: 'i' } },
                        { lastname: { $regex: searchRegex, $options: 'i' } },
                        { 'organizations.name': { $regex: searchRegex, $options: 'i' } },
                        { 'restaurants.restaurantDetails.name': { $regex: searchRegex, $options: 'i' } },
                    ],
                },
            });
        }

        // Sort by creation date (newest first) and email
        pipeline.push({
            $sort: {
                createdAt: -1,
                email: 1,
            },
        });

        // Create count pipeline before adding pagination
        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        // Add pagination to main pipeline
        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        // Execute both pipelines in parallel
        const [users, countResult] = await Promise.all([
            this._usersRepository.aggregate(pipeline, { readPreference: ReadPreference.SECONDARY_PREFERRED }),
            this._usersRepository.aggregate(countPipeline, { readPreference: ReadPreference.SECONDARY_PREFERRED }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: users,
            total,
        };
    }
}
