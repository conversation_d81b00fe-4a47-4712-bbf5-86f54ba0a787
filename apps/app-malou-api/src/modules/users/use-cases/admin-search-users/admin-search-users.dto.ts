import { Transform, Type } from 'class-transformer';
import { IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

export class AdminSearchUsersQueryDto {
    @IsOptional()
    @IsString()
    text?: string;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(0)
    offset?: number = 0;
}
