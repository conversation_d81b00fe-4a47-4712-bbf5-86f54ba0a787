import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { CreateAggregatedWheelOfFortuneBodyDto, UpdateAggregatedWheelOfFortuneBodyDto, WheelOfFortuneDto } from '@malou-io/package-dto';
import { IAggregatedWheelOfFortune, ID, PopulateBuilderHelper, toDbId, toDbIds } from '@malou-io/package-models';
import { GIFT_NAME_MAX_LENGTH, MalouErrorCode, Role } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GiftsUseCases } from ':modules/gifts/gifts.use-cases';
import UsersUseCases from ':modules/users/users.use-cases';
import { WheelOfFortuneSnapshotsUseCases } from ':modules/wheel-of-fortune-snapshots/wheel-of-fortune-snapshots.use-cases';
import { AggregatedWheelsOfFortuneDtoMapper } from ':modules/wheels-of-fortune/aggregated-wheels-of-fortune/aggregated-wheels-of-fortune.dto-mapper';
import { AggregatedWheelsOfFortuneRepository } from ':modules/wheels-of-fortune/aggregated-wheels-of-fortune/aggregated-wheels-of-fortune.repository';
import { RestaurantWheelsOfFortuneRepository } from ':modules/wheels-of-fortune/restaurant-wheels-of-fortune/restaurant-wheels-of-fortune.repository';
import { RestaurantWheelsOfFortuneUseCases } from ':modules/wheels-of-fortune/restaurant-wheels-of-fortune/restaurant-wheels-of-fortune.use-cases';
import { WheelOfFortune as WheelOfFortuneEntity } from ':modules/wheels-of-fortune/wheel-of-fortune.entity';
import { WheelsOfFortuneDtoMapper } from ':modules/wheels-of-fortune/wheels-of-fortune.dto-mapper';
import { WheelsOfFortuneRepository } from ':modules/wheels-of-fortune/wheels-of-fortune.repository';
import { WheelsOfFortuneUseCases } from ':modules/wheels-of-fortune/wheels-of-fortune.use-cases';

@singleton()
export class AggregatedWheelsOfFortuneUseCases {
    constructor(
        private readonly _aggregatedWheelsOfFortuneRepository: AggregatedWheelsOfFortuneRepository,
        private readonly _restaurantWheelsOfFortuneRepository: RestaurantWheelsOfFortuneRepository,
        private readonly _wheelsOfFortuneRepository: WheelsOfFortuneRepository,
        private readonly _giftsUseCases: GiftsUseCases,
        private readonly _restaurantWheelsOfFortuneUseCases: RestaurantWheelsOfFortuneUseCases,
        private readonly _usersUseCases: UsersUseCases,
        private readonly _wheelOfFortuneSnapshotsUseCases: WheelOfFortuneSnapshotsUseCases,
        private readonly _wheelsOfFortuneUseCases: WheelsOfFortuneUseCases,
        private readonly _aggregatedWheelsOfFortuneDtoMapper: AggregatedWheelsOfFortuneDtoMapper,
        private readonly _wheelsOfFortuneDtoMapper: WheelsOfFortuneDtoMapper
    ) {}

    async createAggregatedWheelOfFortune(wheelOfFortuneData: CreateAggregatedWheelOfFortuneBodyDto): Promise<WheelOfFortuneDto> {
        const hasGiftWithTooLongName = wheelOfFortuneData.gifts.some((gift) => gift.name.length > GIFT_NAME_MAX_LENGTH);
        if (hasGiftWithTooLongName) {
            logger.error(
                '[AggregatedWheelsOfFortuneUseCases][createAggregatedWheelOfFortune] Tried to create a wheel of fortune with a gift with a name too long',
                { gifts: wheelOfFortuneData.gifts.filter((gift) => gift.name.length > GIFT_NAME_MAX_LENGTH) }
            );
            throw new MalouError(MalouErrorCode.GIFT_NAME_TOO_LONG, { message: 'One of the gifts has a name too long' });
        }

        const restaurantIds = wheelOfFortuneData.restaurants.map((restaurant) => restaurant.id);
        const doesRestaurantHaveAnActiveWheel = await this._doesRestaurantHaveAnActiveOrProgrammedWheel(restaurantIds);
        if (doesRestaurantHaveAnActiveWheel) {
            logger.error(
                '[AggregatedWheelsOfFortuneUseCases][createAggregatedWheelOfFortune] Tried to create a wheel of fortune with restaurant already having an active or programmed wheel',
                { restaurantIds }
            );
            throw new MalouError(MalouErrorCode.RESTAURANT_HAS_AN_ACTIVE_OR_PROGRAMMED_WHEEL_OF_FORTUNE, {
                message: 'One of the restaurants already has an active wheel',
            });
        }

        const giftIds = await this._giftsUseCases.createGiftsAndGiftStocks(wheelOfFortuneData.gifts);

        const mappedData = {
            giftIds,
            totemIds: toDbIds(wheelOfFortuneData.totemIds),
            parameters: {
                primaryColor: wheelOfFortuneData.parameters.primaryColor,
                secondaryColor: wheelOfFortuneData.parameters.secondaryColor,
                mediaId: wheelOfFortuneData.parameters.media?.id ? toDbId(wheelOfFortuneData.parameters.media?.id) : null,
                giftClaimStartDateOption: wheelOfFortuneData.parameters.giftClaimStartDateOption,
                giftClaimDurationInDays: wheelOfFortuneData.parameters.giftClaimDurationInDays,
                redirectionSettings: {
                    nextDrawEnabledDelay: wheelOfFortuneData.parameters.redirectionSettings.nextDrawEnabledDelay,
                    shouldRedirect: wheelOfFortuneData.parameters.redirectionSettings.shouldRedirect,
                    platforms: wheelOfFortuneData.parameters.redirectionSettings.platforms.map((platform) => ({
                        order: platform.order,
                        platformKey: platform.platformKey,
                    })),
                },
            },
            startDate: wheelOfFortuneData.startDate,
            endDate: wheelOfFortuneData.endDate,
        };
        const aggregatedWheelOfFortune = await this._aggregatedWheelsOfFortuneRepository.create({ data: mappedData });

        await this._updateExistingRestaurantWheelWithNewAggregatedWheel(restaurantIds, aggregatedWheelOfFortune._id);
        await this._createRestaurantWheelsForRestaurantsWithoutOneYet(restaurantIds, aggregatedWheelOfFortune._id);

        if (new WheelOfFortuneEntity(aggregatedWheelOfFortune).isActive()) {
            await this._wheelsOfFortuneUseCases.addWheelOfFortuneTotemsRedirection(
                aggregatedWheelOfFortune._id.toString(),
                aggregatedWheelOfFortune.totemIds.map((id) => id.toString())
            );
        }

        const populatedAggregatedWheelOfFortune = await this._wheelsOfFortuneRepository.findWheelById(
            aggregatedWheelOfFortune._id.toString()
        );

        if (!populatedAggregatedWheelOfFortune) {
            logger.error('[AggregatedWheelsOfFortuneUseCases][createAggregatedWheelOfFortune] Wheel of fortune not found', {
                id: aggregatedWheelOfFortune._id.toString(),
            });
            throw new MalouError(MalouErrorCode.WHEEL_OF_FORTUNE_NOT_FOUND, { message: 'Aggregated wheel of fortune not found' });
        }

        await this._wheelOfFortuneSnapshotsUseCases.saveWheelOfFortuneSnapshot(populatedAggregatedWheelOfFortune._id.toString());

        return this._wheelsOfFortuneDtoMapper.toWheelOfFortuneDto(populatedAggregatedWheelOfFortune);
    }

    async updateAggregatedWheelOfFortune(
        wheelOfFortuneId: string,
        wheelOfFortuneData: UpdateAggregatedWheelOfFortuneBodyDto,
        userId: ID
    ): Promise<WheelOfFortuneDto> {
        const hasGiftWithTooLongName = wheelOfFortuneData.gifts.some((gift) => gift.name.length > GIFT_NAME_MAX_LENGTH);
        if (hasGiftWithTooLongName) {
            logger.error(
                '[AggregatedWheelsOfFortuneUseCases][updateAggregatedWheelOfFortune] Tried to update a wheel of fortune with a gift with a name too long',
                { gifts: wheelOfFortuneData.gifts.filter((gift) => gift.name.length > GIFT_NAME_MAX_LENGTH) }
            );
            throw new MalouError(MalouErrorCode.GIFT_NAME_TOO_LONG, { message: 'One of the gifts has a name too long' });
        }

        const currentAggregatedWheelOfFortune = await this._wheelsOfFortuneRepository.findWheelById(wheelOfFortuneId);
        assert.equal(
            (currentAggregatedWheelOfFortune as any).__t,
            'AggregatedWheelOfFortune',
            'updateAggregatedWheelOfFortune must be called with an aggregated WOF'
        );

        if (!currentAggregatedWheelOfFortune) {
            logger.error('[AggregatedWheelsOfFortuneUseCases][updateAggregatedWheelOfFortune] Restaurant wheel of fortune not found', {
                wheelOfFortuneId,
            });
            throw new MalouError(MalouErrorCode.WHEEL_OF_FORTUNE_NOT_FOUND, { message: 'Aggregated wheel of fortune not found' });
        }

        const restaurantIds = wheelOfFortuneData.restaurants.map((restaurant) => restaurant.id);
        const currentRestaurantIds = currentAggregatedWheelOfFortune.restaurants.map((restaurant) => restaurant._id.toString());
        const restaurantIdsToAddToWheel = restaurantIds.filter((restaurantId) => !currentRestaurantIds.includes(restaurantId));
        const doesRestaurantHaveAnActiveWheel = await this._doesRestaurantHaveAnActiveOrProgrammedWheel(restaurantIdsToAddToWheel);
        if (doesRestaurantHaveAnActiveWheel) {
            logger.error(
                '[AggregatedWheelsOfFortuneUseCases][updateAggregatedWheelOfFortune] Tried to update a wheel of fortune with restaurant already having an active or programmed wheel',
                { restaurantIdsToAddToWheel }
            );
            throw new MalouError(MalouErrorCode.RESTAURANT_HAS_AN_ACTIVE_OR_PROGRAMMED_WHEEL_OF_FORTUNE, {
                message: 'One of the restaurants already has an active wheel',
            });
        }

        const userWithRestaurants = await this._usersUseCases.getUserWithActualRestaurants(userId.toString());
        const currentNonOwnedRestaurantIds = currentRestaurantIds.filter(
            (restaurantId) => !userWithRestaurants.restaurants.some((restaurant) => restaurant.restaurantId.toString() === restaurantId)
        );
        const missingNonOwnedRestaurantIds = currentNonOwnedRestaurantIds.filter((restaurantId) => !restaurantIds.includes(restaurantId));
        const areAllCurrentNonOwnedRestaurantsStillInWheel = missingNonOwnedRestaurantIds.length === 0;
        if (!areAllCurrentNonOwnedRestaurantsStillInWheel) {
            const user = await this._usersUseCases.getUserById(userId.toString());

            if (user.role !== Role.ADMIN) {
                logger.error(
                    '[AggregatedWheelsOfFortuneUseCases][updateAggregatedWheelOfFortune] Tried to update a wheel of fortune without a restaurant not owned by the user',
                    { missingNonOwnedRestaurantIds }
                );
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_MANAGED_MISSING_IN_WHEEL_OF_FORTUNE, {
                    message: 'One of the restaurants not managed by the user is missing in the wheel of fortune',
                });
            }
        }

        const giftIds = await this._giftsUseCases.updateGiftsAndGiftStocks(wheelOfFortuneData.gifts);

        const previousAggregatedWheelOfFortune = await this._aggregatedWheelsOfFortuneRepository.findOne({
            filter: { _id: wheelOfFortuneId },
            options: { lean: true },
        });
        assert(previousAggregatedWheelOfFortune);

        const mappedData = {
            giftIds,
            totemIds: toDbIds(wheelOfFortuneData.totemIds),
            parameters: {
                primaryColor: wheelOfFortuneData.parameters.primaryColor,
                secondaryColor: wheelOfFortuneData.parameters.secondaryColor,
                mediaId: wheelOfFortuneData.parameters.media?.id ? toDbId(wheelOfFortuneData.parameters.media?.id) : null,
                giftClaimStartDateOption: wheelOfFortuneData.parameters.giftClaimStartDateOption,
                giftClaimDurationInDays: wheelOfFortuneData.parameters.giftClaimDurationInDays,
                redirectionSettings: {
                    nextDrawEnabledDelay: wheelOfFortuneData.parameters.redirectionSettings.nextDrawEnabledDelay,
                    shouldRedirect: wheelOfFortuneData.parameters.redirectionSettings.shouldRedirect,
                    platforms: wheelOfFortuneData.parameters.redirectionSettings.platforms.map((platform) => ({
                        order: platform.order,
                        platformKey: platform.platformKey,
                    })),
                },
            },
            startDate: wheelOfFortuneData.startDate,
            endDate: wheelOfFortuneData.endDate,
        };
        const aggregatedWheelOfFortune = await this._aggregatedWheelsOfFortuneRepository.findOneAndUpdate({
            filter: { _id: wheelOfFortuneId },
            update: mappedData,
            options: {
                lean: true,
            },
        });
        assert(aggregatedWheelOfFortune);

        const isCurrentlyActive = new WheelOfFortuneEntity(aggregatedWheelOfFortune).isActive();
        const wasActive = new WheelOfFortuneEntity(previousAggregatedWheelOfFortune).isActive();
        if (isCurrentlyActive) {
            const previousTotems = previousAggregatedWheelOfFortune.totemIds.map((id) => id.toString());
            const totemsToRemove = previousTotems.filter((id) => !wheelOfFortuneData.totemIds.includes(id));

            if (totemsToRemove.length) {
                await this._wheelsOfFortuneUseCases.removeWheelOfFortuneTotemsRedirection(totemsToRemove);
            }
            if (wheelOfFortuneData.totemIds.length) {
                await this._wheelsOfFortuneUseCases.addWheelOfFortuneTotemsRedirection(
                    previousAggregatedWheelOfFortune._id.toString(),
                    wheelOfFortuneData.totemIds
                );
            }
        } else if (wasActive) {
            await this._wheelsOfFortuneUseCases.removeWheelOfFortuneTotemsRedirection(
                previousAggregatedWheelOfFortune.totemIds.map((id) => id.toString())
            );
        }

        const restaurantIdsToRemoveFromWheel = currentAggregatedWheelOfFortune.restaurants
            .filter((restaurant) => !restaurantIds.includes(restaurant._id.toString()))
            .map((restaurant) => restaurant._id.toString());
        await this._updateExistingRestaurantWheelWithNewAggregatedWheel(restaurantIdsToRemoveFromWheel, null);
        await this._updateExistingRestaurantWheelWithNewAggregatedWheel(restaurantIds, aggregatedWheelOfFortune._id);
        await this._createRestaurantWheelsForRestaurantsWithoutOneYet(restaurantIds, aggregatedWheelOfFortune._id);

        const populatedAggregatedWheelOfFortune = await this._wheelsOfFortuneRepository.findWheelById(
            aggregatedWheelOfFortune._id.toString()
        );
        assert(populatedAggregatedWheelOfFortune);

        await this._wheelOfFortuneSnapshotsUseCases.saveWheelOfFortuneSnapshot(populatedAggregatedWheelOfFortune._id.toString());

        return this._wheelsOfFortuneDtoMapper.toWheelOfFortuneDto(populatedAggregatedWheelOfFortune);
    }

    async getUserActiveAndScheduledAggregatedWheelsOfFortune(userId: ID): Promise<WheelOfFortuneDto[]> {
        const userWithRestaurants = await this._usersUseCases.getUserWithActualRestaurants(userId.toString());
        const userRestaurantIds: string[] = userWithRestaurants.restaurants.map((restaurant) => restaurant.restaurantId.toString());

        const aggregatedWheelsOfFortune =
            await this._aggregatedWheelsOfFortuneRepository.findDeeplyPopulatedAggregatedWheelOfFortuneByRestaurantIds(userRestaurantIds, {
                startDate: { $exists: true, $ne: null },
                $or: [{ endDate: { $gte: new Date() } }, { endDate: null }],
            });

        const sortedAggregatedWheelsOfFortune = this._sortWheelsAndGifts(aggregatedWheelsOfFortune);

        return sortedAggregatedWheelsOfFortune.map((aggregatedWheelOfFortune) =>
            this._aggregatedWheelsOfFortuneDtoMapper.toWheelOfFortuneDto(aggregatedWheelOfFortune)
        );
    }

    private _sortWheelsAndGifts<T extends PopulateBuilderHelper<IAggregatedWheelOfFortune, [{ path: 'gifts' }]>>(
        aggregatedWheelsOfFortune: T[]
    ): T[] {
        const sortedAggregatedWheelsOfFortune = aggregatedWheelsOfFortune.sort((wofA, wofB) => (wofA.createdAt > wofB.createdAt ? 1 : -1));
        sortedAggregatedWheelsOfFortune.forEach((aggregatedWheelOfFortune) =>
            aggregatedWheelOfFortune.gifts.sort((giftA, giftB) => (giftA.createdAt > giftB.createdAt ? 1 : -1))
        );
        return sortedAggregatedWheelsOfFortune;
    }

    private async _updateExistingRestaurantWheelWithNewAggregatedWheel(
        restaurantIds: string[],
        aggregatedWheelOfFortuneId: ID | null
    ): Promise<void> {
        await this._restaurantWheelsOfFortuneRepository.updateMany({
            filter: { restaurantId: { $in: restaurantIds } },
            update: { aggregatedWheelOfFortuneId: aggregatedWheelOfFortuneId },
        });
    }

    private async _createRestaurantWheelsForRestaurantsWithoutOneYet(
        restaurantIds: string[],
        aggregatedWheelOfFortuneId: ID
    ): Promise<void> {
        const restaurantsWithoutAnExistingWheelId =
            await this._restaurantWheelsOfFortuneRepository.getRestaurantsWithoutAnExistingWheelId(restaurantIds);
        const wheelsToCreate = restaurantsWithoutAnExistingWheelId.map((restaurantId) =>
            this._restaurantWheelsOfFortuneUseCases.buildDefaultRestaurantWheelOfFortune(restaurantId, aggregatedWheelOfFortuneId)
        );
        await this._restaurantWheelsOfFortuneRepository.createMany({ data: wheelsToCreate });
    }

    private async _doesRestaurantHaveAnActiveOrProgrammedWheel(restaurantIds: string[]): Promise<boolean> {
        const { restaurantsWithActiveAggregatedWheel, restaurantsWithActiveWheel } =
            await this._wheelsOfFortuneUseCases.getRestaurantsWithActiveOrProgrammedWheel(restaurantIds);

        return restaurantsWithActiveAggregatedWheel.length !== 0 || restaurantsWithActiveWheel.length !== 0;
    }
}
