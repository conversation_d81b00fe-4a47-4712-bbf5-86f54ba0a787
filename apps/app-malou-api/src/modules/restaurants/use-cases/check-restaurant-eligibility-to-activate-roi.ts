import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import {
    FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER,
    getNumberOfMonthsSinceMostRecentDate,
    isNotNil,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { CreateRoiActivatedNotificationUseCase } from ':modules/notifications/use-cases/create-roi-activated-notification/create-roi-activated-notification.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { AlertFeature } from ':services/alerts/alerts.interface';
import { SlackAlertsService } from ':services/alerts/slack/slack-alerts.service';

@singleton()
export default class CheckRestaurantEligibilityToActivateRoi {
    private readonly MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI = FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER + 1;
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackAlertsService: SlackAlertsService,
        private readonly _createRoiActivatedNotificationUseCase: CreateRoiActivatedNotificationUseCase
    ) {}

    async execute(): Promise<void> {
        const now = DateTime.now();
        const createdAtStartMonth = now
            .minus({ month: this.MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI + 1 })
            .startOf('month')
            .startOf('day');
        const createdAtEndMonth = createdAtStartMonth.endOf('month').endOf('day');

        const allActiveRestaurantsCreated = await this._restaurantsRepository.getAllActiveRestaurantsToCheckRoiEligibility({
            startDate: createdAtStartMonth.toJSDate(),
            endDate: createdAtEndMonth.toJSDate(),
        });
        const eligibleRestaurantsToActivateRoi: string[] = [];

        for (const restaurant of allActiveRestaurantsCreated) {
            if (
                getNumberOfMonthsSinceMostRecentDate([restaurant.createdAt, restaurant.openingDate].filter(isNotNil)) >=
                    this.MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI &&
                !restaurant.roiActivated
            ) {
                eligibleRestaurantsToActivateRoi.push(restaurant._id.toString());
            }
        }

        if (!eligibleRestaurantsToActivateRoi.length) {
            logger.info('No restaurant is eligible to activate Roi');
        } else {
            await this._restaurantsRepository.upsertManyRestaurantsRoiStatus({
                restaurantsIds: eligibleRestaurantsToActivateRoi,
                activateRoi: true,
            });

            logger.info(`Saving ${eligibleRestaurantsToActivateRoi.length} restaurants...`);
            logger.info(`Restaurants saved ${eligibleRestaurantsToActivateRoi.join(', ')}`);

            await this._createRoiActivatedNotificationUseCase.execute(eligibleRestaurantsToActivateRoi);
        }

        await this._sendSlackAlert(eligibleRestaurantsToActivateRoi, allActiveRestaurantsCreated);
    }

    private async _sendSlackAlert(
        restaurantIds: string[],
        allRestaurants: Pick<IRestaurant, '_id' | 'name'>[]
    ): Promise<{ success: boolean }> {
        const title = `ROI ACTIVATION JOB ==> ${restaurantIds.length} restaurants activated (finished at ${new Date().toISOString()})`;
        const details = restaurantIds
            .map((restaurantId) => {
                const associatedRestaurant = allRestaurants.find(({ _id }) => _id.toString() === restaurantId?.toString());
                if (!associatedRestaurant) {
                    return restaurantId;
                }
                return `- ${associatedRestaurant.name} (${associatedRestaurant._id?.toString()})`;
            })
            .join(', \n');
        return this._slackAlertsService.sendAlert(
            AlertFeature.ROI_ACTIVATED_RESTAURANTS,
            new MalouError(MalouErrorCode.ROI_ACTIVATION_MESSAGE, {
                message: title,
            }),
            'none (jobs)',
            `RESTAURANTS UPDATED : \n\n ${details} \n`,
            '-'
        );
    }
}
