import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import { autoInjectable, container } from 'tsyringe';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>ale, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { JsonToExcel } from ':helpers/excel';
import StringEncryptorToSafeUrl from ':helpers/string-encryptor-to-safe-url/string-encryptor-to-safe-url';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import { Diagnostic } from ':modules/diagnostics/entities/diagnostic.entity';
import { InstagramPageDiscoveryService } from ':modules/diagnostics/services/get-instagram-page/instagram-page-discovery.service';
import { CreateOrRetrieveDiagnosticUseCase } from ':modules/diagnostics/use-cases/create-diagnostic/create-diagnostic.use-case';
import { UpdateDiagnosticWithInconsistenciesUseCase } from ':modules/diagnostics/use-cases/get-detected-inconsistencies.use-case';
import { UpdateDiagnosticWithReviewCountForSimilarRestaurantsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-average-review-count.use-case';
import { UpdateDiagnosticWithGoogleRatingUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-google-rating.use-case';
import { UpdateDiagnosticWithInstagramAccountUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-instagram-account.use-case';
import { UpdateDiagnosticWithInstagramRatingUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-instagram-rating.use-case';
import { UpdateDiagnosticWithKeywordsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-keywords.use-case';
import { UpdateDiagnosticWithReviewsAnalysesUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-reviews-analyses.use-case';
import { UpdateDiagnosticWithSimilarRestaurantsUseCase } from ':modules/diagnostics/use-cases/update-diagnostic-with-similar-restaurants.use-case';
import ':plugins/db';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';
import { DIAGNOSTIC_PLACE_IDS, INSTAGRAM_URLS } from ':tasks/diagnostics/diagnostic-place-ids.constants';

const DEFAULT_EXCEL_CELL_VALUE = 'N/A';

interface BulkCreateDiagnosticsTaskData {
    rawData?: {
        useInstagramUrls: boolean;
    };
    googleSheetData?: {
        googleSheetId: string;
        sheetId?: string;
        placeIdColumnIndex: number;
        instagramColumnIndex: number;
    };
}

/**
 * Use this to get data from a Google Sheet or from raw data
 * You cant use both at the same time
 */
const taskData: BulkCreateDiagnosticsTaskData = {
    googleSheetData: {
        googleSheetId: '1MEtRKoRUYrorIlkhTel_X4uiy6BGaKDXsDiaaabZJxA',
        sheetId: '2026776045', // gid
        placeIdColumnIndex: 0,
        instagramColumnIndex: 2,
    },
    // rawData: {
    //     useInstagramUrls: true,
    // },
};

@autoInjectable()
class BulkCreateDiagnosticsTask {
    diagnosticId: string = '';

    constructor(
        private readonly _createOrRetrieveDiagnosticUseCase: CreateOrRetrieveDiagnosticUseCase,
        private readonly _updateDiagnosticWithInconsistenciesUseCase: UpdateDiagnosticWithInconsistenciesUseCase,
        private readonly _updateDiagnosticWithKeywordsUseCase: UpdateDiagnosticWithKeywordsUseCase,
        private readonly _updateDiagnosticWithSimilarRestaurantsUseCase: UpdateDiagnosticWithSimilarRestaurantsUseCase,
        private readonly _updateDiagnosticWithGoogleRatingUseCase: UpdateDiagnosticWithGoogleRatingUseCase,
        private readonly _updateDiagnosticWithReviewsAnalysesUseCase: UpdateDiagnosticWithReviewsAnalysesUseCase,
        private readonly _updateDiagnosticWithReviewCountForSimilarRestaurantsUseCase: UpdateDiagnosticWithReviewCountForSimilarRestaurantsUseCase,
        private readonly _updateDiagnosticWithInstagramAccountUseCase: UpdateDiagnosticWithInstagramAccountUseCase,
        private readonly _instagramPageDiscoveryService: InstagramPageDiscoveryService,
        private readonly _updateDiagnosticWithInstagramRatingUseCase: UpdateDiagnosticWithInstagramRatingUseCase,
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _googleSheetsService: GoogleSheetsService
    ) {}

    async execute() {
        if (!taskData.googleSheetData && !taskData.rawData) {
            throw new Error('You must provide either googleSheetData or rawData');
        }
        if (taskData.googleSheetData && taskData.rawData) {
            throw new Error('You cant use both googleSheetData and rawData at the same time');
        }
        const { placeIds, instagramUrls } = await this._getPlaceIdsAndInstagram(taskData);
        try {
            await this._createDiagnostics(placeIds, instagramUrls);
        } catch (error) {
            console.error('Error while creating diagnostics:', error);
        }
        const diagnostics = await this._diagnosticsRepository.getLastDiagnosticsByPlaceIds(placeIds);
        console.log('diagnostics :>> ', diagnostics.length);
        const excelData = diagnostics.map((diagnostic) => this._toExcelData(diagnostic));
        const now = DateTime.now().toFormat('yyyy-MM-dd_HH-mm-ss');
        await this.downloadData(excelData, `diagnostics_${now}.xlsx`);
    }

    private async _getPlaceIdsAndInstagram(bulkCreateDiagnosticsTaskData: BulkCreateDiagnosticsTaskData): Promise<{
        placeIds: string[];
        instagramUrls: string[];
    }> {
        if (bulkCreateDiagnosticsTaskData.googleSheetData) {
            return this._getPlaceIdsAndInstagramFromGoogleSheet(bulkCreateDiagnosticsTaskData.googleSheetData);
        }
        return this._getPlaceIdsAndInstagramFromRawData(bulkCreateDiagnosticsTaskData.rawData);
    }

    private async _getPlaceIdsAndInstagramFromGoogleSheet(googleSheetData: BulkCreateDiagnosticsTaskData['googleSheetData']): Promise<{
        placeIds: string[];
        instagramUrls: string[];
    }> {
        if (!googleSheetData || !googleSheetData.sheetId) {
            throw new Error('You must provide a valid googleSheet');
        }
        const doc = await this._googleSheetsService.loadGoogleSheet(googleSheetData.googleSheetId);
        const sheet = doc.sheetsById[googleSheetData.sheetId];
        const rows = await sheet.getRows();
        const placeIds = rows.map((row) => row._rawData[googleSheetData.placeIdColumnIndex]);
        const instagramUrls = rows.map((row) => row._rawData[googleSheetData.instagramColumnIndex]);
        return {
            placeIds,
            instagramUrls,
        };
    }

    private _getPlaceIdsAndInstagramFromRawData(rawData: BulkCreateDiagnosticsTaskData['rawData']): {
        placeIds: string[];
        instagramUrls: string[];
    } {
        const useInstagramUrls = rawData?.useInstagramUrls;
        return {
            placeIds: DIAGNOSTIC_PLACE_IDS,
            instagramUrls: useInstagramUrls ? INSTAGRAM_URLS : [],
        };
    }

    private async _createDiagnostics(placeIds: string[], instagramUrls: string[]) {
        const placeIdsInError: string[] = [];
        const lang = MaloupeLocale.FR;
        for (let i = 0; i < placeIds.length; i++) {
            const placeId = placeIds[i];
            const instagramUrl: string | undefined = instagramUrls[i];
            console.time('Done for ' + placeId);
            let diagnostic;
            try {
                diagnostic = await this._createOrRetrieveDiagnosticUseCase.execute({ placeId, lang });
            } catch (error) {
                console.error('Error while creating diagnostic:', {
                    placeId,
                    error,
                });
                placeIdsInError.push(placeId);
                continue;
            }
            const diagnosticId = diagnostic.id;
            try {
                await this._updateDiagnosticWithKeywordsUseCase.execute(diagnosticId);
                await this._updateDiagnosticWithReviewsAnalysesUseCase.execute(diagnosticId);
                await this._updateDiagnosticWithInconsistenciesUseCase.execute(diagnosticId);
                await this._updateDiagnosticWithSimilarRestaurantsUseCase.execute(diagnosticId);
                await this._updateDiagnosticWithGoogleRatingUseCase.execute(diagnosticId);
                await this._updateDiagnosticWithReviewCountForSimilarRestaurantsUseCase.execute(diagnosticId);
                if (instagramUrl) {
                    const instagramPageName = instagramUrl.split('instagram.com/')[1];
                    const instagramPage = await this._instagramPageDiscoveryService.getPage(instagramPageName);
                    if (instagramPage) {
                        await this._updateDiagnosticWithInstagramAccountUseCase.execute(
                            diagnosticId,
                            instagramPage.business_discovery.username
                        );
                        await this._updateDiagnosticWithInstagramRatingUseCase.execute(diagnosticId);
                    }
                }
            } catch (error) {
                console.error('Error while processing diagnostic:', {
                    placeId,
                    diagnosticId,
                    error,
                });
                placeIdsInError.push(placeId);
            }
            console.log(diagnosticId);
            console.timeEnd('Done for ' + placeId);
            await waitFor(2 * TimeInMilliseconds.SECOND);
        }
        console.log('placeIdsInError :>> ', placeIdsInError.length, JSON.stringify(placeIdsInError));
    }

    private _toExcelData(diagnostic: Diagnostic): Record<string, any> {
        const chosenKeywords = diagnostic.getKeywords() ?? [];
        const baseDiagnosticUrl = `https://diagnostic.malou.io/diagnostic`;
        return {
            placeId: diagnostic.placeId,
            name: diagnostic.restaurant?.name,
            categoryNameFr: diagnostic.restaurant?.category?.categoryName?.fr,
            score: diagnostic.getTotalScore(),
            scoreRating: diagnostic.getTotalScoreRating(),
            inconsistenciesCount: diagnostic.inconsistencies?.inconsistencyCount ?? DEFAULT_EXCEL_CELL_VALUE,
            platformCount: diagnostic.inconsistencies?.platformCount ?? DEFAULT_EXCEL_CELL_VALUE,
            inconsistenciesRating: diagnostic.getInconsistencyRating(),
            photosRating: diagnostic.getPhotosRating(),
            servicesRating: diagnostic.getServicesRating(),
            keyword1Fr: chosenKeywords[0]?.keyword?.fr ?? DEFAULT_EXCEL_CELL_VALUE,
            keyword1En: chosenKeywords[0]?.keyword?.en ?? DEFAULT_EXCEL_CELL_VALUE,
            position1: chosenKeywords[0]?.restaurantRanking ?? DEFAULT_EXCEL_CELL_VALUE,
            keyword2Fr: chosenKeywords[1]?.keyword?.fr ?? DEFAULT_EXCEL_CELL_VALUE,
            keyword2En: chosenKeywords[1]?.keyword?.en ?? DEFAULT_EXCEL_CELL_VALUE,
            position2: chosenKeywords[1]?.restaurantRanking ?? DEFAULT_EXCEL_CELL_VALUE,
            keywordsRating: diagnostic.getKeywordsRating(),
            googleNote: diagnostic.restaurant?.rating ?? DEFAULT_EXCEL_CELL_VALUE,
            similarRestaurantsNote: diagnostic.restaurant?.averageRatingForSimilarRestaurants ?? DEFAULT_EXCEL_CELL_VALUE,
            googleNoteRating: diagnostic.getGoogleRating(),
            reviewCount: diagnostic.reviewCount ?? DEFAULT_EXCEL_CELL_VALUE,
            averageReviewCountForSimilarRestaurants: diagnostic.averageReviewCountForSimilarRestaurants ?? DEFAULT_EXCEL_CELL_VALUE,
            semanticIndicationFr: diagnostic.semanticAnalysisOverviewByLang?.[MaloupeLocale.FR] ?? DEFAULT_EXCEL_CELL_VALUE,
            semanticIndicationEn: diagnostic.semanticAnalysisOverviewByLang?.[MaloupeLocale.EN] ?? DEFAULT_EXCEL_CELL_VALUE,
            diagnosticUrl: `${baseDiagnosticUrl}/${diagnostic.malouDiagnosticId}`,
            encryptedDiagnosticUrl: `${baseDiagnosticUrl}/${new StringEncryptorToSafeUrl().encryptUuidToSafeUrl(
                diagnostic.malouDiagnosticId
            )}`,
        };
    }

    private async downloadData(data: any[], fileName: string) {
        console.log('[DOWNLOAD_DATA]');
        const xlsxParams = {
            data,
            wsOpts: { skipHeaders: false },
            filepath: `${process.env.HOME}/Downloads/${fileName}`,
        };
        const jsonToExcel = new JsonToExcel(xlsxParams);
        jsonToExcel.saveFile();
    }
}

const task = container.resolve(BulkCreateDiagnosticsTask);
task.execute()
    .then(() => {
        console.log('Finish BulkCreateDiagnosticsTask');
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
