import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import { container, singleton } from 'tsyringe';

import { YextAddRequestStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import UpdateAddRequestStatusUseCase from ':modules/publishers/yext/use-cases/update-add-request-status/update-add-request-status.use-case';
import ':plugins/db';

@singleton()
class CheckReviewLocationTask {
    constructor(
        private readonly _yextLocationRepository: YextLocationRepository,
        private readonly _updateAddRequestStatusUseCase: UpdateAddRequestStatusUseCase
    ) {}

    async execute() {
        const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();
        const yextLocationsInReview = await this._yextLocationRepository.find({
            filter: { addRequestStatus: YextAddRequestStatus.REVIEW },
            projection: { _id: 1 },
            options: { lean: true },
        });
        logger.info(`Found ${yextLocationsInReview.length} yext location in REVIEW`);
        for (const yextLocation of yextLocationsInReview) {
            await this._updateAddRequestStatusUseCase.execute(yextLocation._id.toString());
        }
        logger.info('DONE');
    }
}

const task = container.resolve(CheckReviewLocationTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
