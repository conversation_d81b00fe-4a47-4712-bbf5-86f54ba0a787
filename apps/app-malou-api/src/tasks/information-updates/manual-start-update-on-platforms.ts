import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { StartUpdateOnPlatformsUseCase } from ':modules/platforms/use-cases/start-update-on-platforms/start-update-on-platforms.use-case';
import ':plugins/db';

@singleton()
class ManualStartUpdateOnPlatformsTask {
    constructor(
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _startUpdateOnPlatformsUseCase: StartUpdateOnPlatformsUseCase
    ) {}

    async execute() {
        const informationUpdateId = '68021fafd79f8d3231eeebcf'; // To be filled with the information update id you want to update on platforms

        if (!informationUpdateId) {
            logger.info('Please provide the information update id to update on platforms');
        }

        const informationUpdate = await this._informationUpdatesRepository.findOne({
            filter: { _id: toDbId(informationUpdateId) },
            options: { lean: true },
        });

        if (!informationUpdate) {
            logger.error('Information update not found');
            return;
        }
        const { platformStates } = await this._startUpdateOnPlatformsUseCase.execute(informationUpdate);
        await this._informationUpdatesRepository.findOneAndUpdate({
            filter: { _id: toDbId(informationUpdateId) },
            update: {
                platformStates,
            },
        });

        logger.info('informationUpdate updated with these platformStates', platformStates);
    }
}

const task = container.resolve(ManualStartUpdateOnPlatformsTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
