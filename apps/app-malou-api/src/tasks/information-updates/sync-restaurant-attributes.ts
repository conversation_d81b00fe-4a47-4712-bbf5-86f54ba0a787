import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import * as gmbPlatformsUseCases from ':modules/platforms/platforms/gmb/use-cases';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class SyncRestaurantAttributesTask {
    private readonly _ORGANIZATION_ID = '655e40ed9d2240f0d1f4bce4';

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _gmbMapper: GmbMapper,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository
    ) {}

    async execute() {
        const restaurants = await this._restaurantsRepository.find({
            filter: { organizationId: toDbId(this._ORGANIZATION_ID) },
            options: { lean: true },
            projection: { _id: 1 },
        });

        for (const restaurant of restaurants) {
            try {
                const platformData = await gmbPlatformsUseCases.getOverviewData({ restaurantId: restaurant._id.toString() });
                const malouData = await this._gmbMapper.toMalouMapper(platformData, ['attributes']);

                // link attributes to created restaurant
                await Promise.all(
                    malouData.attributeList.map(({ attributeValue, attributeId }) =>
                        this._restaurantAttributesRepository.upsert({
                            filter: { restaurantId: restaurant._id, attributeId },
                            update: { attributeValue },
                        })
                    )
                );

                logger.info(`Syncing attributes for restaurant: ${restaurant._id}`, {
                    malouData,
                });
            } catch (err) {
                logger.error(`Error syncing attributes for restaurant: ${restaurant._id}`, {
                    error: err,
                });
            }
        }
    }
}

const checkRestaurantUrlsTask = container.resolve(SyncRestaurantAttributesTask);
checkRestaurantUrlsTask
    .execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
