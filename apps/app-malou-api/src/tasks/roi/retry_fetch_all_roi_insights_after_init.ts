import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import { autoInjectable, container } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import { getLastXMonthsBeforeCurrentMonth, isNotNil } from '@malou-io/package-utils';

import { Restaurant } from ':modules/restaurants/entities/restaurant.entity';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { CreateMessageQueuesToPerformMonthlySaveRoiInsightsUseCase } from ':modules/roi-insights/use-cases/create-message-queues-to-perform-monthly-save-roi-insights/create-message-queues-to-perform-monthly-save-roi-insights.use-case';
import { RoiSettingsRepository } from ':modules/roi-settings/roi-settings.repository';
import ':plugins/db';

// One time only task to fetch roi insights in the pas for all restaurants impacted by this bug
// https://airtable.com/appIqBldyX7wZlWnp/tblbOxMTpexQyxSTV/viwVSdtBlz857nQiA/recyA6ZTFFN6Qpzt7

@autoInjectable()
class RetryFetchAllRoiInsightsAfterInitTask {
    constructor(
        private readonly _roiSettingsRepository: RoiSettingsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _createMessageQueuesToPerformMonthlySaveRoiInsightsUseCase: CreateMessageQueuesToPerformMonthlySaveRoiInsightsUseCase
    ) {}

    async execute() {
        this._createMessageQueuesToPerformMonthlySaveRoiInsightsUseCase.initialize();

        const restaurants = await this._getImpactedRestaurants();
        await Promise.all(restaurants.map((restaurant) => this._fetchRoiInsightsFromBeginning(restaurant)));
        console.log(`Updated roi insights for ${restaurants.length} restaurants: ${restaurants.map((r) => r.name).join(', ')}`);
    }

    private async _getImpactedRestaurants(): Promise<IRestaurant[]> {
        const releaseDate = DateTime.fromJSDate(new Date('2025-06-01')).startOf('day').toJSDate();
        const recentlyCreatedRoiSettings = await this._roiSettingsRepository.find({
            filter: { createdAt: { $gte: releaseDate } },
            projection: { restaurantId: 1 },
            options: { lean: true },
        });
        const restaurantsWithRecentlyCreatedRoiSettings = recentlyCreatedRoiSettings
            .map((roiSettings) => roiSettings.restaurantId)
            .filter(isNotNil);
        const impactedRestaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: restaurantsWithRecentlyCreatedRoiSettings }, openingDate: null },
            options: { lean: true },
        });
        return impactedRestaurants;
    }

    private async _fetchRoiInsightsFromBeginning(restaurant: IRestaurant): Promise<void> {
        const monthNumberSinceCreation = new Restaurant(restaurant).getNumberOfMonthSinceCreatedAndOpened();

        const allHistoryMonths = getLastXMonthsBeforeCurrentMonth(monthNumberSinceCreation - 1);

        await Promise.all(
            allHistoryMonths.map(({ month, year }) =>
                this._createMessageQueuesToPerformMonthlySaveRoiInsightsUseCase.sendMessage({
                    restaurantId: restaurant._id.toString(),
                    month,
                    year,
                })
            )
        );
    }
}

const task = container.resolve(RetryFetchAllRoiInsightsAfterInitTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
