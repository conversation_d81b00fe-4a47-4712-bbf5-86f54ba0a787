import 'reflect-metadata';

import ':env';

import parse from 'csv-parse/lib/sync';
import fs from 'fs';
import { autoInjectable, container } from 'tsyringe';

import { FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER, getNumberOfMonthsSinceMostRecentDate, isNotNil } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

interface AirtableRow {
    id_restaurant_malouapp: string;
    venue: string;
    current_category_from_products: string;
}

enum ContractTypes {
    App = 'App',
    Copilot = 'Copilot',
    Ultimate = 'Ultimate',
}

@autoInjectable()
class ActivateRoiFeature {
    readonly MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI = FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER + 1;
    readonly ALL_CONTRACT_TYPES = [ContractTypes.App, ContractTypes.Copilot, ContractTypes.Ultimate];
    readonly ACCEPTED_CONTRACT_TYPES = [ContractTypes.App];
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute() {
        const restaurants = await this._restaurantsRepository.getAllActiveRestaurantsToCheckRoiEligibility({});

        const airtableData: AirtableRow[] = await this._readCsvFile('src/tasks/roi/airtable-contract-data.csv');

        const eligibleRestaurantsToActivateRoi: string[] = [];

        const notFoundRestaurants: string[] = [];
        const restaurantsWithNoValidConditions: string[] = [];
        let copilotRestaurants = 0;
        for (const restaurant of restaurants) {
            const airtableRestaurant = airtableData.find(
                ({ id_restaurant_malouapp }) => id_restaurant_malouapp === restaurant._id.toString()
            );
            if (!airtableRestaurant) {
                notFoundRestaurants.push(`${restaurant.name} (${restaurant._id})`);
                console.log(`[ERROR] No associated restaurant found for ${restaurant._id} - ${restaurant.name}`);
                continue;
            }
            if (!this.ALL_CONTRACT_TYPES.includes(airtableRestaurant.current_category_from_products as ContractTypes)) {
                const airtableErrorText = `${airtableRestaurant.current_category_from_products} (${airtableRestaurant.venue} on Airtable)`;
                console.log(`[ERROR] No valid contract found for restaurant ${restaurant._id} - ${restaurant.name} : ${airtableErrorText}`);
                continue;
            }
            if (!this.ACCEPTED_CONTRACT_TYPES.includes(airtableRestaurant.current_category_from_products as ContractTypes)) {
                copilotRestaurants++;
                console.log(
                    `Skipping restaurant ${restaurant._id}- ${restaurant.name}  with contract ${airtableRestaurant.current_category_from_products}`
                );
                continue;
            }
            if (
                getNumberOfMonthsSinceMostRecentDate([restaurant.createdAt, restaurant.openingDate].filter(isNotNil)) >=
                    this.MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI &&
                !restaurant.roiActivated
            ) {
                eligibleRestaurantsToActivateRoi.push(restaurant._id.toString());
            } else {
                restaurantsWithNoValidConditions.push(`${restaurant.name} (${restaurant._id.toString()})`);
            }
        }

        if (!eligibleRestaurantsToActivateRoi.length) {
            console.log('No restaurant is eligible to activate Roi');
        } else {
            await this._restaurantsRepository.upsertManyRestaurantsRoiStatus({
                restaurantsIds: eligibleRestaurantsToActivateRoi,
                activateRoi: true,
            });

            console.log(`Found ${restaurants.length} restaurants in db`);
            console.log(`Saving ${eligibleRestaurantsToActivateRoi.length} restaurants...`);
            console.log(`INFO : Skipping ${copilotRestaurants} copilot restaurants`);
            console.log(`INFO : ${restaurantsWithNoValidConditions.length} restaurants not respecting the conditions`);
            console.log(`WARNING : No Airtable data for ${notFoundRestaurants.length} restaurants`);
        }
    }

    private async _readCsvFile(path: string): Promise<AirtableRow[]> {
        try {
            const binary = fs.readFileSync(path);
            return parse(binary, {
                columns: true,
                skipEmptyLines: true,
                relax: true,
                relax_column_count: true,
                skip_lines_with_empty_values: true,
                skip_empty_lines: true,
                delimiter: [',', ';'],
                skip_lines_with_error: true,
            });
        } catch (err) {
            console.error(`Error finding or reading file ${err}`);
            return [];
        }
    }
}

const task = container.resolve(ActivateRoiFeature);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
