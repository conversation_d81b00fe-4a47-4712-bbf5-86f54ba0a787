import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import qs from 'qs';
import { container, singleton } from 'tsyringe';

import { ApplicationLanguage, CountryCode } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';

interface GmbCategory {
    name: string; // gmd id
    displayName: string;
}

@singleton()
export class GetGmbCategoriesTask {
    private readonly _GMB_TOKEN = ''; // You must set a valid GMB token to run the use case successfully (search for latest credentials with 'authId: '<EMAIL>')

    async execute() {
        if (!this._GMB_TOKEN) {
            logger.error('GMB token is not set. Please set a valid GMB token to run the task.');
            throw new Error('GMB token is not set');
        }

        const url = 'https://mybusinessbusinessinformation.googleapis.com/v1/categories';
        const params = { languageCode: ApplicationLanguage.FR, regionCode: CountryCode.FRANCE, view: 'BASIC' };
        const headers = { Authorization: `Bearer ${this._GMB_TOKEN}` };

        try {
            const config: AxiosRequestConfig = {
                params,
                headers,
                paramsSerializer: function (param) {
                    return qs.stringify(param, { arrayFormat: 'repeat' });
                },
            };

            const response: AxiosResponse<{ categories: GmbCategory[] }> = await axios.get(url, config);
            const categories = response.data.categories.map((category) => ({
                categoryId: category.name.split('/')[1],
                categoryName: category.displayName,
            }));
            logger.info('[GET GMB CATEGORIES] Fetched categories', {
                count: categories.length,
            });
        } catch (error) {
            logger.error('Error while fetching GMB categories', error);
            throw error;
        }
    }
}

const task = container.resolve(GetGmbCategoriesTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
