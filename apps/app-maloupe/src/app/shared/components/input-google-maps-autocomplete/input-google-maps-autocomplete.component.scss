@use '_malou_variables.scss' as *;

::ng-deep .pac-container {
    @apply box-content border-t-0 bg-white py-2.5 pr-9;
    border-radius: 20px;

    box-shadow: 0px 0px 14px 0px rgba(106, 82, 253, 0.07);

    .pac-item {
        @apply flex h-8 items-center border-0 px-4 text-malou-text;
        font-family: $malou-font-family;

        .pac-icon {
            display: none;
        }

        .pac-item-query {
            @apply text-malou-text;
            font-size: 15px;
            font-weight: 400;

            .pac-matched {
                font-weight: 600;
            }
        }

        span {
            @apply overflow-hidden;
            font-size: 15px;
            font-weight: 400;
        }

        &:hover {
            background-color: theme('backgroundColor.malou-dark');
        }
    }

    .pac-item-selected {
        background-color: theme('backgroundColor.malou-dark');
        font-size: 14px;
        font-weight: 600;
    }

    &::after {
        display: none;
    }
}

::ng-deep .google-input-custom-option-panel {
    background: transparent !important;
    .mat-mdc-autocomplete-panel {
        overflow: hidden !important;
    }

    .mdc-list-item__primary-text {
        width: 100% !important;
    }
}

::ng-deep .mat-mdc-progress-spinner circle {
    stroke: theme('colors.malou-primary') !important;
}
