/// <reference types="@types/googlemaps" />
import {
    AfterViewInit,
    Component,
    effect,
    ElementRef,
    inject,
    input,
    OnInit,
    output,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { ControlValueAccessor, FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import intersection from 'lodash.intersection';
import { debounceTime, distinctUntilChanged, filter, map, startWith, Subject, takeUntil, tap } from 'rxjs';

import { CountryCode, TimeInMilliseconds } from '@malou-io/package-utils';

import { AuthContext } from ':core/context/auth.context';
import { LimitExceededPopinComponent } from ':shared/components/limit-exceeded-popin/limit-exceeded-popin.component';
import { LIMITS_AND_TIME_BY_LOCAL_KEY } from ':shared/constants';
import { RequestLocalStorageKey, SvgIcon } from ':shared/enums';
import { ApplyPurePipe } from ':shared/pipes';
import { buildNewRequestDetails, increaseRequestCount } from ':shared/utils';

const HANDLED_AUTOCOMPLETE_TYPES = ['bar', 'cafe', 'food', 'lodging', 'restaurant'];

const AVAILABLE_COUNTRIES = [
    CountryCode.FRANCE,
    CountryCode.UNITED_STATES,
    CountryCode.BELGIUM,
    CountryCode.SWITZERLAND,
    CountryCode.UNITED_KINGDOM,
];

const BIAS_RADIUS_IN_METERS = 1000000;
@Component({
    selector: 'app-input-google-maps-autocomplete',
    templateUrl: './input-google-maps-autocomplete.component.html',
    styleUrls: ['./input-google-maps-autocomplete.component.scss'],
    providers: [{ provide: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, useValue: { overlayPanelClass: 'google-input-custom-option-panel' } }],
    imports: [
        FormsModule,
        MatIconModule,
        MatFormFieldModule,
        MatAutocompleteModule,
        MatProgressSpinnerModule,
        ReactiveFormsModule,
        TranslateModule,
        MatTooltipModule,
        ApplyPurePipe,
    ],
})
export class InputGoogleMapsAutocompleteComponent implements OnInit, ControlValueAccessor, AfterViewInit {
    placeholder = input<string>('');
    value = input<string>('');
    placeIdsToFilter = input<string[]>([]);

    /**
     * Will add a colored border and will display the error below the input
     */
    errorMessage = input<string | undefined>(undefined);

    /**
     * reset the input value
     */
    reset = input<boolean>();

    /**
     * On value change output
     */
    inputGoogleMapsAutocompleteChange = output<string>();

    /**
     * On location selected output (see: googleMapsAutocomplete)
     */
    locationSelected = output<google.maps.places.PlaceResult>();

    @ViewChild('addressInput') addressInput: ElementRef | undefined;

    readonly SvgIcon = SvgIcon;

    /**
     * To easily retrieve changed value event form input
     */
    control = new UntypedFormControl();

    isFocused = false;
    isEmptyValue = true;

    readonly killSubscriptions$: Subject<void> = new Subject();

    /**
     * For implementing rate limiting
     */
    private readonly _authContext = inject(AuthContext);
    private readonly _dialog = inject(MatDialog);

    getPlaceDiagnosticRateLimitPerDayExceeded = input<boolean>();

    readonly searchRestaurantRateLimitPerMinuteExceeded: WritableSignal<boolean> = signal(false);
    readonly getPlaceDetailsRateLimitPerHourExceeded: WritableSignal<boolean> = signal(false);

    searchRestaurantRequestCount = 0;
    searchRestaurantStartTime: number | null = null;

    /**
     * For implementing Google Maps Autocomplete
     */

    predictedPlaces: WritableSignal<google.maps.places.AutocompletePrediction[]> = signal([]);
    private _autoCompleteService: google.maps.places.AutocompleteService | undefined;
    private _placesService: google.maps.places.PlacesService | undefined;
    private _autoCompleteSessionToken: google.maps.places.AutocompleteSessionToken | undefined;
    private _franceLocation: google.maps.LatLng | undefined;

    readonly isMalouUserLoggedIn = this._authContext.isTokenValid();

    /**
     * For implementing ControlValueAccessor
     */
    isTouched = false;
    onTouched: any = () => {};
    onChange: any = () => {};

    constructor() {
        effect(() => {
            if (this.reset()) {
                this.control.setValue('');
                this.predictedPlaces.set([]);
            }

            if (this.value() && this.value() !== this.control.value && !this.control.disabled && this.value() !== '') {
                this.control.setValue(this.value());
            }
        });
    }

    ngOnInit(): void {
        this._initRateLimiting();
        this._initControl();
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this._initAutoComplete();
        }, 1000);
    }

    setEmptyValue(value: string): void {
        this.isEmptyValue = value === '';
    }

    propagateValue(value: string): void {
        this.markAsTouched();
        this.onChange(value);
        this.inputGoogleMapsAutocompleteChange.emit(value);
    }

    locationSelectedFn(event: MatAutocompleteSelectedEvent): void {
        if (!this._isGetPlaceDetailsLimitExceeded()) {
            const value = event.option.value;
            const index = this.predictedPlaces().findIndex((place) => place.description === value);
            const { place_id, description } = this.predictedPlaces()[index];
            this._getPlaceDetails(place_id, description);
        }
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    markAsTouched(): void {
        if (!this.isTouched) {
            this.onTouched();
            this.isTouched = true;
        }
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    writeValue(value: string): void {
        this.control.setValue(value);
    }

    shouldDisplayEyeIcon = (types: string[]): boolean =>
        this.isMalouUserLoggedIn && intersection(HANDLED_AUTOCOMPLETE_TYPES, types).length > 0;

    private _initControl(): void {
        this.control.valueChanges
            .pipe(
                startWith(''),
                debounceTime(300),
                distinctUntilChanged(),
                tap((value) => {
                    this.setEmptyValue(value);
                    this.propagateValue(value);
                }),
                map((value) => (typeof value === 'string' ? value : '')),
                filter((value) => value.length >= 3 && !this.control.disabled),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe((value) => {
                this._getPlaceAutocomplete(value);
            });
    }

    /**
     * Rate limiting for Google Maps Places API
     */

    private _initRateLimiting(): void {
        this.searchRestaurantRequestCount = 0;
        this.searchRestaurantStartTime = null;
        this.searchRestaurantRateLimitPerMinuteExceeded.set(this._limitRestaurantSearchApiCall({ isInit: true }));
        this.getPlaceDetailsRateLimitPerHourExceeded.set(this._isGetPlaceDetailsLimitExceeded());
        if (this.getPlaceDiagnosticRateLimitPerDayExceeded()) {
            this.control.disable();
        }
    }

    private _initAutoComplete(): void {
        this._autoCompleteService = new google.maps.places.AutocompleteService();
        this._autoCompleteSessionToken = new google.maps.places.AutocompleteSessionToken();
        this._placesService = new google.maps.places.PlacesService(this.addressInput?.nativeElement);
        this._franceLocation = new google.maps.LatLng(46.603354, 1.888334);
    }

    private _getPlaceAutocomplete(value: string): void {
        if (!this._autoCompleteSessionToken) {
            this._autoCompleteSessionToken = new google.maps.places.AutocompleteSessionToken();
        }
        if (this._autoCompleteService) {
            this._limitRestaurantSearchApiCall({ isInit: false });
            this._autoCompleteService.getPlacePredictions(
                {
                    input: value,
                    sessionToken: this._autoCompleteSessionToken,
                    types: this.isMalouUserLoggedIn ? [] : HANDLED_AUTOCOMPLETE_TYPES,
                    componentRestrictions: { country: AVAILABLE_COUNTRIES },
                    location: this._franceLocation,
                    radius: BIAS_RADIUS_IN_METERS,
                },
                (predictions, status) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK) {
                        this.predictedPlaces.set(predictions.filter((place) => !this.placeIdsToFilter().includes(place.place_id)));
                    }
                }
            );
        }
    }

    private _getPlaceDetails(placeId: string, description: string): void {
        if (this._placesService) {
            const key = RequestLocalStorageKey.GET_PLACE_DETAILS_REQUEST;
            const newCount = increaseRequestCount({ key });
            const isLimitExceeded = newCount >= LIMITS_AND_TIME_BY_LOCAL_KEY[key].limit && !this.isMalouUserLoggedIn;
            this._setPlaceDetailsExceededRateLimit(isLimitExceeded);
            this._placesService.getDetails(
                { placeId, sessionToken: this._autoCompleteSessionToken, fields: ['place_id'] },
                (result, status) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK) {
                        this.locationSelected.emit({ ...result, formatted_address: description });
                        this._autoCompleteSessionToken = new google.maps.places.AutocompleteSessionToken();
                    }
                }
            );
        }
    }

    private _limitRestaurantSearchApiCall({ isInit }: { isInit: boolean }): boolean {
        const newSearchRequestDetails = buildNewRequestDetails({
            key: RequestLocalStorageKey.SEARCH_REQUEST,
            shouldIncreaseCount: !isInit,
        });
        localStorage.setItem(RequestLocalStorageKey.SEARCH_REQUEST, JSON.stringify(newSearchRequestDetails));

        if (
            newSearchRequestDetails.requestCount <= LIMITS_AND_TIME_BY_LOCAL_KEY[RequestLocalStorageKey.SEARCH_REQUEST].limit ||
            this.isMalouUserLoggedIn
        ) {
            this.searchRestaurantRateLimitPerMinuteExceeded.set(false);
            this.control.enable();
            return false;
        }
        this.searchRestaurantRateLimitPerMinuteExceeded.set(true);
        this.control.disable();

        const elapsedTime = Date.now() - newSearchRequestDetails.lastUpdatedAt;
        const timeBeforeEnable = LIMITS_AND_TIME_BY_LOCAL_KEY[RequestLocalStorageKey.SEARCH_REQUEST].time - elapsedTime;
        const enableTime = Math.min(TimeInMilliseconds.SECOND * 15, timeBeforeEnable);
        setTimeout(() => {
            this.control.enable();
            localStorage.setItem(
                RequestLocalStorageKey.SEARCH_REQUEST,
                JSON.stringify({
                    requestCount: 0,
                    lastUpdatedAt: Date.now(),
                })
            );
        }, enableTime);
        return true;
    }

    private _isGetPlaceDetailsLimitExceeded(): boolean {
        const key = RequestLocalStorageKey.GET_PLACE_DETAILS_REQUEST;
        const newPlaceRequestDetails = buildNewRequestDetails({
            key,
            shouldIncreaseCount: false,
        });

        localStorage.setItem(key, JSON.stringify(newPlaceRequestDetails));

        const isLimitExceeded = newPlaceRequestDetails.requestCount >= LIMITS_AND_TIME_BY_LOCAL_KEY[key].limit && !this.isMalouUserLoggedIn;

        this._setPlaceDetailsExceededRateLimit(isLimitExceeded);
        return isLimitExceeded;
    }

    private _setPlaceDetailsExceededRateLimit(value: boolean): void {
        this.getPlaceDetailsRateLimitPerHourExceeded.set(value);
        if (value) {
            this.control.disable();
            this._dialog.open(LimitExceededPopinComponent, {
                height: '250px',
                width: '600px',
                panelClass: 'malou-dialog-panel',
            });
            return;
        }
        this.control.enable();
    }
}
