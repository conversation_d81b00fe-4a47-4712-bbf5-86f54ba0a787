import { Inject, Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';

import { CsvInsightChart, getPlatformKeysWithFollowers, PlatformKey } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import {
    AggregatedAverageReviewsRatingsCsvInsightsService,
    AggregatedFollowersCsvInsightsService,
    AggregatedGmbVisibilityCsvInsightsServiceV2,
    AggregatedKeywordSearchImpressionsService,
    AggregatedKeywordsService,
    AggregatedPublicationsCsvInsightsService,
    AggregatedReviewCountCsvInsightsService,
    AggregatedStoriesCsvInsightsService,
    PlatformsRatingsAggregatedCsvInsightsService,
} from ':shared/services/csv-services/aggregated-insights';
import { AggregatedBoostersGiftsCsvInsightService } from ':shared/services/csv-services/aggregated-insights/aggregated-boosters-gifts.service';
import { AggregatedBoostersReviewsCountCsvInsightV2Service } from ':shared/services/csv-services/aggregated-insights/aggregated-boosters-reviews-count-v2.service';
import { AggregatedBoostersReviewsCountCsvInsightService } from ':shared/services/csv-services/aggregated-insights/aggregated-boosters-reviews-count.service';
import { AggregatedBoostersScanCountCsvInsightV2Service } from ':shared/services/csv-services/aggregated-insights/aggregated-boosters-scan-count-v2.service';
import { AggregatedBoostersScanCountCsvInsightService } from ':shared/services/csv-services/aggregated-insights/aggregated-boosters-scan-count.service';
import { AggregatedSemanticAnalysisByCategoryCsvInsightsService } from ':shared/services/csv-services/aggregated-insights/aggregated-semantic-analysis-by-category-insights.service';
import { AggregatedSemanticAnalysisTopTopicsCsvInsightsService } from ':shared/services/csv-services/aggregated-insights/aggregated-semantic-analysis-top-topics-insights.service';
import { CsvAsStringArrays, CsvService } from ':shared/services/csv-services/csv-service.abstract';
import { CsvStringCreator } from ':shared/services/csv-services/helpers/create-csv-string';
import {
    BoostersGiftsCsvInsightService,
    BoostersReviewsCountCsvInsightService,
    BoostersScanCountCsvInsightService,
    FollowersCsvInsightsService,
    GmbVisibilityCsvInsightsService,
    KeywordsCsvInsightsService,
    KeywordsCsvInsightsServiceV2,
    PlatformsRatingsCsvInsightsService,
    PublicationsCsvInsightsService,
    ReviewsRatingsEvolutionCsvInsightsService,
    ReviewsRatingsTotalCsvInsightsService,
    StoryCsvInsightsService,
    TopKeywordSearchImpressionsService,
} from ':shared/services/csv-services/insights';
import { BoostersReviewsCountCsvInsightV2Service } from ':shared/services/csv-services/insights/boosters-reviews-count-v2.service';
import { BoostersScanCountCsvInsightV2Service } from ':shared/services/csv-services/insights/boosters-scan-count-v2.service';
import { FollowersCsvInsightsV2Service } from ':shared/services/csv-services/insights/followers-v2.service';
import { PublicationsCsvInsightsV2Service } from ':shared/services/csv-services/insights/publications-v2.service';
import { SemanticAnalysisDetailsCsvInsightsService } from ':shared/services/csv-services/insights/semantic-analysis-details-csv-insights.service';
import { SemanticAnalysisTopicsCsvInsightsService } from ':shared/services/csv-services/insights/semantic-analysis-topics-csv-insights.service';

@Injectable({ providedIn: 'root' })
export class DownloadCsvInsightsService {
    constructor(
        @Inject(KeywordsCsvInsightsService)
        private readonly _keywordsCsvInsightsService: CsvService,
        @Inject(KeywordsCsvInsightsServiceV2)
        private readonly _keywordsCsvInsightsServiceV2: CsvService,
        @Inject(GmbVisibilityCsvInsightsService)
        private readonly _gmbVisibilityCsvInsightsService: CsvService,
        @Inject(StoryCsvInsightsService)
        private readonly _storiesCsvInsightsService: CsvService,
        @Inject(PlatformsRatingsCsvInsightsService)
        private readonly _platformsRatingsCsvInsightsService: CsvService,
        @Inject(PlatformsRatingsAggregatedCsvInsightsService)
        private readonly _platformsRatingsAggregatedCsvInsightsService: CsvService,
        @Inject(AggregatedStoriesCsvInsightsService)
        private readonly _aggregatedStoriesCsvInsightsService: CsvService,
        @Inject(AggregatedFollowersCsvInsightsService)
        private readonly _aggregatedFollowersCsvInsightsService: CsvService,
        @Inject(AggregatedPublicationsCsvInsightsService)
        private readonly _aggregatedPublicationsCsvInsightsService: CsvService,
        @Inject(AggregatedReviewCountCsvInsightsService)
        private readonly _aggregatedReviewCountCsvInsightsService: CsvService,
        @Inject(AggregatedAverageReviewsRatingsCsvInsightsService)
        private readonly _aggregatedAverageReviewsRatingsCsvInsightsService: CsvService,
        @Inject(ReviewsRatingsEvolutionCsvInsightsService)
        private readonly _reviewsRatingsEvolutionCsvInsightsService: CsvService,
        @Inject(ReviewsRatingsTotalCsvInsightsService)
        private readonly _reviewsRatingsTotalCsvInsightsService: CsvService,
        @Inject(AggregatedGmbVisibilityCsvInsightsServiceV2)
        private readonly _aggregatedGmbVisibilityCsvInsightsServiceV2: CsvService,
        @Inject(SemanticAnalysisTopicsCsvInsightsService)
        private readonly _semanticAnalysisTopicsCsvInsightsService: CsvService,
        @Inject(SemanticAnalysisDetailsCsvInsightsService)
        private readonly _semanticAnalysisDetailsCsvInsightsService: CsvService,
        @Inject(AggregatedSemanticAnalysisTopTopicsCsvInsightsService)
        private readonly _aggregatedSemanticAnalysisTopTopicsCsvInsightsService: CsvService,
        @Inject(AggregatedSemanticAnalysisByCategoryCsvInsightsService)
        private readonly _aggregatedSemanticAnalysisByCategoryInsightsService: CsvService,
        private readonly _csvStringCreator: CsvStringCreator,
        private readonly _experimentationService: ExperimentationService,
        // TODO: TO REMOVE AFTER FEATURE FLAG REMOVAL
        @Inject(BoostersScanCountCsvInsightService)
        private readonly _boostersScanCountInsightsService: CsvService,
        @Inject(BoostersReviewsCountCsvInsightService)
        private readonly _boostersReviewsCountInsightsService: CsvService,
        @Inject(AggregatedBoostersScanCountCsvInsightService)
        private readonly _aggregatedBoostersScanCountCsvInsightService: CsvService,
        @Inject(AggregatedBoostersReviewsCountCsvInsightService)
        private readonly _aggregatedBoostersReviewsCountCsvInsightService: CsvService,
        @Inject(FollowersCsvInsightsService)
        private readonly _followersCsvInsightsService: CsvService,
        @Inject(PublicationsCsvInsightsService)
        private readonly _publicationsCsvInsightsService: CsvService,
        // -----------------------------
        @Inject(BoostersScanCountCsvInsightV2Service)
        private readonly _boostersScanCountInsightsV2Service: CsvService,
        @Inject(BoostersReviewsCountCsvInsightV2Service)
        private readonly _boostersReviewsCountInsightsV2Service: CsvService,
        @Inject(BoostersGiftsCsvInsightService)
        private readonly _boostersGiftsInsightsService: CsvService,
        @Inject(AggregatedBoostersScanCountCsvInsightV2Service)
        private readonly _aggregatedBoostersScanCountCsvInsightV2Service: CsvService,
        @Inject(AggregatedBoostersGiftsCsvInsightService)
        private readonly _aggregatedBoostersGiftsCsvInsightsService: CsvService,
        @Inject(AggregatedBoostersReviewsCountCsvInsightV2Service)
        private readonly _aggregatedBoostersReviewsCountCsvInsightV2Service: CsvService,
        @Inject(FollowersCsvInsightsV2Service)
        private readonly _followersCsvInsightsV2Service: CsvService,
        @Inject(PublicationsCsvInsightsV2Service)
        private readonly _publicationsCsvInsightsV2Service: CsvService,
        @Inject(TopKeywordSearchImpressionsService)
        private readonly _topKeywordSearchImpressionsService: CsvService,
        @Inject(AggregatedKeywordSearchImpressionsService)
        private readonly _aggregatedKeywordSearchImpressionsService: CsvService,
        @Inject(AggregatedKeywordsService)
        private readonly _aggregatedKeywordsService: CsvService
    ) {}

    getCsvInsightsData$(csvChart: CsvInsightChart): Observable<string | null> {
        let csvData$: Observable<CsvAsStringArrays | null>;
        switch (csvChart) {
            case CsvInsightChart.KEYWORDS:
                csvData$ = this._experimentationService.isFeatureEnabled$('release-keywords-insights-v2')
                    ? this._keywordsCsvInsightsServiceV2.getCsvData$()
                    : this._keywordsCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.GMB_VISIBILITY:
                csvData$ = this._gmbVisibilityCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_GMB_VISIBILITY:
                csvData$ = this._aggregatedGmbVisibilityCsvInsightsServiceV2.getCsvData$();
                break;
            case CsvInsightChart.PUBLICATIONS:
                csvData$ = this._publicationsCsvInsightsV2Service.getCsvData$();
                break;
            case CsvInsightChart.STORIES:
                csvData$ = this._storiesCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.ALL_FOLLOWERS:
                csvData$ = this._followersCsvInsightsV2Service.getCsvData$({
                    platformKeys: getPlatformKeysWithFollowers(),
                });
                break;
            case CsvInsightChart.FB_FOLLOWERS:
                csvData$ = this._followersCsvInsightsV2Service.getCsvData$({ platformKeys: [PlatformKey.FACEBOOK] });
                break;
            case CsvInsightChart.IG_FOLLOWERS:
                csvData$ = this._followersCsvInsightsV2Service.getCsvData$({ platformKeys: [PlatformKey.INSTAGRAM] });
                break;
            case CsvInsightChart.PLATFORMS_RATINGS:
                csvData$ = this._platformsRatingsCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.SEMANTIC_ANALYSIS_TOPICS:
                csvData$ = this._experimentationService.isFeatureEnabledForRestaurant('release-new-semantic-analysis')
                    ? this._semanticAnalysisTopicsCsvInsightsService.getCsvData$()
                    : of(null);
                break;
            case CsvInsightChart.SEMANTIC_ANALYSIS_DETAILS:
                csvData$ = this._experimentationService.isFeatureEnabledForRestaurant('release-new-semantic-analysis')
                    ? this._semanticAnalysisDetailsCsvInsightsService.getCsvData$()
                    : of(null);
                break;
            case CsvInsightChart.BOOSTERS_SCAN_COUNT:
                csvData$ = this._boostersScanCountInsightsV2Service.getCsvData$();
                break;
            case CsvInsightChart.BOOSTERS_REVIEWS_COUNT:
                csvData$ = this._boostersReviewsCountInsightsV2Service.getCsvData$();
                break;
            case CsvInsightChart.BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION:
                csvData$ = this._boostersGiftsInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_PLATFORMS_RATINGS:
                csvData$ = this._platformsRatingsAggregatedCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_STORIES:
                csvData$ = this._aggregatedStoriesCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_ALL_FOLLOWERS:
                csvData$ = this._aggregatedFollowersCsvInsightsService.getCsvData$({
                    platformKeys: getPlatformKeysWithFollowers(),
                });
                break;
            case CsvInsightChart.AGGREGATED_FB_FOLLOWERS:
                csvData$ = this._aggregatedFollowersCsvInsightsService.getCsvData$({ platformKeys: [PlatformKey.FACEBOOK] });
                break;
            case CsvInsightChart.AGGREGATED_IG_FOLLOWERS:
                csvData$ = this._aggregatedFollowersCsvInsightsService.getCsvData$({ platformKeys: [PlatformKey.INSTAGRAM] });
                break;
            case CsvInsightChart.AGGREGATED_PUBLICATIONS:
                csvData$ = this._aggregatedPublicationsCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_REVIEW_COUNT:
                csvData$ = this._aggregatedReviewCountCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_AVERAGE_REVIEWS_RATINGS:
                csvData$ = this._aggregatedAverageReviewsRatingsCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_SEMANTIC_ANALYSIS_TOP_TOPICS:
                csvData$ = this._experimentationService.isFeatureEnabled('release-new-aggregated-semantic-analysis')
                    ? this._aggregatedSemanticAnalysisTopTopicsCsvInsightsService.getCsvData$()
                    : of(null);
                break;
            case CsvInsightChart.AGGREGATED_SEMANTIC_ANALYSIS_BY_CATEGORY:
                csvData$ = this._experimentationService.isFeatureEnabled('release-new-aggregated-semantic-analysis')
                    ? this._aggregatedSemanticAnalysisByCategoryInsightsService.getCsvData$()
                    : of(null);
                break;
            case CsvInsightChart.AGGREGATED_BOOSTERS_SCAN_COUNT:
                csvData$ = this._aggregatedBoostersScanCountCsvInsightV2Service.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION:
                csvData$ = this._aggregatedBoostersGiftsCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_BOOSTERS_REVIEWS_COUNT:
                csvData$ = this._aggregatedBoostersReviewsCountCsvInsightV2Service.getCsvData$();
                break;
            case CsvInsightChart.REVIEWS_RATINGS_EVOLUTION:
                csvData$ = this._reviewsRatingsEvolutionCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.REVIEWS_RATINGS_TOTAL:
                csvData$ = this._reviewsRatingsTotalCsvInsightsService.getCsvData$();
                break;
            case CsvInsightChart.KEYWORD_SEARCH_IMPRESSIONS:
                csvData$ = this._topKeywordSearchImpressionsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_TOP_SEARCH_KEYWORDS:
                csvData$ = this._aggregatedKeywordSearchImpressionsService.getCsvData$();
                break;
            case CsvInsightChart.AGGREGATED_RANKINGS:
                csvData$ = this._aggregatedKeywordsService.getCsvData$();
                break;
            default:
                return of(null);
        }

        return csvData$.pipe(
            map((csvData) => {
                if (!csvData) {
                    return null;
                }
                return this._csvStringCreator.create(csvData);
            })
        );
    }
}
